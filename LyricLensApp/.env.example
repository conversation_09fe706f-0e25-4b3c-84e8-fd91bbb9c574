# LyricLens+ Environment Configuration
# Copy this file to .env and add your actual API keys

# Flask Configuration
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///lyriclens.db

# AI API Keys
GROK_API_KEY=your-grok-api-key-here
DEEPSEEK_API_KEY=your-deepseek-api-key-here

# Optional: Database URL for production
# DATABASE_URL=postgresql://user:password@localhost/lyriclens

# Optional: Upload folder
# UPLOAD_FOLDER=uploads
