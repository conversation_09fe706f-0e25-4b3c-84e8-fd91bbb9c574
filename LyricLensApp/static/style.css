body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    overflow-x: hidden;
}

/* Animated background particles */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(-60px) rotate(240deg); }
}

.container {
    max-width: 900px;
    margin: 20px auto;
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 40px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

header h1 {
    color: white;
    margin: 0;
    font-size: 2.5em;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2em;
    margin: 10px 0 0;
    font-weight: 400;
    position: relative;
    z-index: 1;
}

h1 {
    color: #333;
    margin: 0;
    font-size: 2em;
}

h3, h4 {
    color: #444;
}

form {
    margin-bottom: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    background: rgba(255, 255, 255, 0.7);
    padding: 25px;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

div {
    margin: 10px 0;
    flex: 1 1 100%;
}

label {
    display: block;
    margin-bottom: 8px;
    color: #4a5568;
    font-weight: 600;
    font-size: 0.95em;
}

textarea, input[type="file"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    box-sizing: border-box;
    font-family: inherit;
    font-size: 14px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

textarea:focus, input[type="file"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

textarea {
    resize: vertical;
    min-height: 120px;
}

button, .donation-btn, .print-btn {
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

button:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

button:hover::before {
    left: 100%;
}

.donation-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
}

.donation-btn:hover {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.5);
}

.print-btn {
    background: linear-gradient(90deg, #ff6b6b, #ff8f8f); /* Red gradient */
    color: white;
    margin-bottom: 20px;
}

.print-btn:hover {
    background: linear-gradient(90deg, #e55a5a, #e77d7d);
}

pre {
    background-color: #f8f8f8;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #333;
}

ul {
    list-style-type: none;
    padding-left: 0;
}

ul ul {
    padding-left: 20px;
    list-style-type: disc;
}

li {
    margin: 10px 0;
    color: #333;
}

strong {
    color: #222;
}

footer {
    text-align: center;
    padding: 10px;
    color: #fff;
    background: rgba(0, 0, 0, 0.7);
    width: 100%;
}

/* Add tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
    border-bottom: 1px dotted #666;
    color: #666;
    font-size: 0.9em;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Rhyme analysis styles */
.rhyme-analysis {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.rhyme-lines {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background-color: white;
}

.rhyme-legend {
    font-size: 0.9em;
    color: #555;
    margin-top: 10px;
    padding: 10px;
    border-left: 3px solid #9d50bb;
}

/* Genre classification styles */
.genre-analysis {
    background-color: #f0f8ff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.genre-bars {
    margin: 15px 0;
}

.genre-bar-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.genre-label {
    width: 120px;
    font-weight: bold;
    color: #333;
}

.genre-bar {
    flex-grow: 1;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 10px;
}

.genre-fill {
    height: 100%;
    background: linear-gradient(90deg, #6e48aa, #9d50bb);
    border-radius: 10px;
}

.genre-percentage {
    width: 50px;
    text-align: right;
    font-weight: bold;
}

.genre-note {
    font-size: 0.9em;
    color: #555;
    margin-top: 15px;
    padding: 10px;
    border-left: 3px solid #6e48aa;
    background-color: rgba(157, 80, 187, 0.1);
}

/* Export section styles */
.export-section {
    background-color: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-top: 30px;
    text-align: center;
    border: 2px solid #e9ecef;
}

.export-section h3 {
    color: #6e48aa;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.4em;
}

.export-section p {
    color: #666;
    margin-bottom: 20px;
    font-size: 1em;
}

/* Export buttons styles */
.export-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-bottom: 0;
}

.export-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: background 0.3s;
}

.main-export-btn {
    background: linear-gradient(90deg, #6f42c1, #8e44ad);
    color: white;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    min-width: 160px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-export-btn:hover {
    background: linear-gradient(90deg, #5a32a3, #7d3c98);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.pdf-btn {
    background: linear-gradient(90deg, #d9534f, #ff6b6b);
}

.pdf-btn:hover {
    background: linear-gradient(90deg, #c9302c, #e55a5a);
}

.csv-btn {
    background: linear-gradient(90deg, #5cb85c, #7ed17e);
}

.csv-btn:hover {
    background: linear-gradient(90deg, #449d44, #6ac06a);
}

.json-btn {
    background: linear-gradient(90deg, #f0ad4e, #ffc107);
}

.json-btn:hover {
    background: linear-gradient(90deg, #ec971f, #e0a800);
}

.print-btn {
    background: linear-gradient(90deg, #17a2b8, #20c997);
    color: white;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    min-width: 160px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.print-btn:hover {
    background: linear-gradient(90deg, #138496, #1e7e34);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Mode selector styles */
.mode-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.7);
    padding: 8px;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mode-btn {
    padding: 12px 24px;
    margin: 0 4px;
    background: transparent;
    color: #667eea;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    position: relative;
}

.mode-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #5a6fd8;
}

.mode-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transform: translateY(-1px);
}

/* Comparison form styles */
.comparison-container {
    display: flex;
    gap: 20px;
}

.comparison-column {
    flex: 1;
    padding: 15px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
}

.comparison-column h3 {
    text-align: center;
    margin-top: 0;
    color: #6e48aa;
}

/* Comparison results styles */
.comparison-results {
    margin-top: 20px;
}

.comparison-grid {
    display: grid;
    grid-template-columns: 1fr 2fr 2fr;
    gap: 10px;
    margin-bottom: 20px;
}

.comparison-header {
    font-weight: bold;
    text-align: center;
    padding: 10px;
    background-color: #6e48aa;
    color: white;
    border-radius: 5px 5px 0 0;
}

.comparison-row-header {
    font-weight: bold;
    padding: 10px;
    background-color: #f0e6ff;
    color: #333;
    border-radius: 5px 0 0 5px;
}

.comparison-cell {
    padding: 10px;
    background-color: white;
    border: 1px solid #ddd;
}

.comparison-insights {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
}

.comparison-insights h3 {
    color: #6e48aa;
    margin-top: 0;
}

.comparison-insights ul {
    padding-left: 20px;
}

.comparison-insights li {
    margin-bottom: 8px;
}

/* Export buttons */
.export-buttons {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;
}

.export-btn, .print-btn {
    padding: 8px 15px;
    margin-left: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
}

.print-btn {
    background-color: #f8f9fa;
    color: #333;
}

.pdf-btn {
    background-color: #dc3545;
    color: white;
}

.csv-btn {
    background-color: #28a745;
    color: white;
}

.json-btn {
    background-color: #ffc107;
    color: #333;
}

.export-btn:hover, .print-btn:hover {
    opacity: 0.9;
}

/* Storytelling Analysis styles */
.storytelling-analysis {
    background-color: #f0f8ff;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    border-left: 4px solid #4a90e2;
}

.storytelling-overview {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.storytelling-strength {
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
    text-transform: uppercase;
    font-size: 0.9em;
}

.storytelling-strength.excellent {
    background-color: #d4edda;
    color: #155724;
}

.storytelling-strength.good {
    background-color: #d1ecf1;
    color: #0c5460;
}

.storytelling-strength.moderate {
    background-color: #fff3cd;
    color: #856404;
}

.storytelling-strength.weak {
    background-color: #f8d7da;
    color: #721c24;
}

.storytelling-elements {
    display: grid;
    gap: 20px;
}

.storytelling-element {
    background-color: white;
    padding: 18px;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    border-left: 4px solid #4a90e2;
}

.storytelling-element h4 {
    margin-top: 0;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 1.1em;
}

.element-description {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 12px;
    font-style: italic;
}

.storytelling-list {
    list-style: none;
    padding-left: 0;
}

.storytelling-list li {
    background-color: #f8f9fa;
    padding: 8px 12px;
    margin-bottom: 6px;
    border-radius: 6px;
    border-left: 3px solid #4a90e2;
}

.storytelling-list .no-elements {
    background-color: #fff3cd;
    color: #856404;
    border-left-color: #ffc107;
    font-style: italic;
}

.storytelling-tips {
    background-color: #e8f5e8;
    padding: 18px;
    border-radius: 10px;
    margin-top: 20px;
    border-left: 4px solid #28a745;
}

.storytelling-tips h4 {
    margin-top: 0;
    color: #155724;
}

.storytelling-tips ul {
    margin-bottom: 0;
}

.storytelling-tips li {
    margin-bottom: 8px;
    color: #155724;
}

/* Artist Detection styles */
.artist-detection-analysis {
    background-color: #fff8e1;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    border-left: 4px solid #ff9800;
}

.artist-detection-header {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.artist-detection-header h3 {
    margin-top: 0;
    color: #e65100;
    font-size: 1.2em;
}

.artist-matches {
    background-color: white;
    padding: 18px;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    border-left: 4px solid #ff9800;
    margin-bottom: 20px;
}

.ai-response-content {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #ddd;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    max-height: 400px;
    overflow-y: auto;
}

.ai-response-content pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.artist-detection-tips {
    background-color: #e8f5e8;
    padding: 18px;
    border-radius: 10px;
    margin-top: 20px;
    border-left: 4px solid #4caf50;
}

.artist-detection-tips h4 {
    margin-top: 0;
    color: #2e7d32;
}

.artist-detection-tips ul {
    margin-bottom: 0;
}

.artist-detection-tips li {
    margin-bottom: 8px;
    color: #2e7d32;
}

/* Similar Works styles */
.similar-works {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.similar-works-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.similar-work-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: relative;
    transition: transform 0.3s, box-shadow 0.3s;
}

.similar-work-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.work-type-badge {
    position: absolute;
    top: -10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: bold;
    color: white;
}

.work-type-badge.song {
    background: linear-gradient(90deg, #1DB954, #1ED760);
}

.work-type-badge.literature {
    background: linear-gradient(90deg, #8E44AD, #9B59B6);
}

.work-type-badge.poetry {
    background: linear-gradient(90deg, #D35400, #E67E22);
}

.similar-work-card h4 {
    margin-top: 5px;
    margin-bottom: 10px;
    color: #333;
}

.work-creator {
    color: #666;
    font-style: italic;
    margin-bottom: 10px;
}

.work-reason {
    font-size: 0.9em;
    color: #555;
}

.similar-works-note {
    font-size: 0.9em;
    color: #555;
    margin-top: 15px;
    padding: 10px;
    border-left: 3px solid #6e48aa;
    background-color: rgba(157, 80, 187, 0.1);
}

/* For comparison view */
.similar-works-list {
    padding-left: 15px;
}

.similar-works-list li {
    margin-bottom: 12px;
}

.similar-works-list small {
    color: #666;
    font-style: italic;
}

/* Authentication styles */
.auth-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.auth-form .form-group {
    margin-bottom: 15px;
}

.auth-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.auth-form input[type="text"],
.auth-form input[type="email"],
.auth-form input[type="password"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.auth-form .checkbox {
    display: flex;
    align-items: center;
}

.auth-form .checkbox input {
    margin-right: 10px;
}

.auth-form .checkbox label {
    margin-bottom: 0;
}

.auth-links {
    margin-top: 20px;
    text-align: center;
}

.auth-links a {
    color: #6e48aa;
    text-decoration: none;
}

.auth-links a:hover {
    text-decoration: underline;
}

/* Dashboard styles */
.dashboard {
    padding: 20px 0;
}

.dashboard-section {
    margin-bottom: 30px;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.analysis-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.analysis-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.analysis-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    font-size: 1.2em;
}

.analysis-date {
    color: #777;
    font-size: 0.9em;
    margin-bottom: 10px;
}

.analysis-preview {
    margin-bottom: 15px;
    color: #555;
    font-size: 0.95em;
    line-height: 1.4;
}

.analysis-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.badge.public {
    background-color: #4caf50;
    color: white;
}

.badge.private {
    background-color: #f44336;
    color: white;
}

.badge.shared {
    background-color: #2196f3;
    color: white;
}

.badge.can-edit {
    background-color: #ff9800;
    color: white;
}

.badge.view-only {
    background-color: #9e9e9e;
    color: white;
}

.analysis-actions {
    display: flex;
    gap: 10px;
}

.btn {
    display: inline-block;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    font-size: 0.9em;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    transition: background-color 0.2s;
}

.view-btn {
    background-color: #6e48aa;
    color: white;
}

.view-btn:hover {
    background-color: #5a3a8a;
}

.share-btn {
    background-color: #2196f3;
    color: white;
}

.share-btn:hover {
    background-color: #0b7dda;
}

.delete-btn {
    background-color: #f44336;
    color: white;
}

.delete-btn:hover {
    background-color: #d32f2f;
}

.empty-state {
    color: #777;
    font-style: italic;
    margin-top: 10px;
}

.empty-state a {
    color: #6e48aa;
    text-decoration: none;
}

.empty-state a:hover {
    text-decoration: underline;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
    width: 100%;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.cancel-btn {
    background-color: #9e9e9e;
    color: white;
}

.cancel-btn:hover {
    background-color: #757575;
}

/* Share page styles */
.share-container {
    padding: 20px 0;
}

.analysis-preview {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.preview-text {
    font-style: italic;
    color: #555;
    margin-bottom: 10px;
}

.preview-date {
    color: #777;
    font-size: 0.9em;
}

.public-note, .private-note {
    margin-top: 5px;
    font-size: 0.9em;
    color: #555;
}

.public-toggle-form {
    margin-top: 15px;
}

.toggle-btn {
    background-color: #9c27b0;
    color: white;
}

.toggle-btn:hover {
    background-color: #7b1fa2;
}

.share-form-container {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.share-form .form-group {
    margin-bottom: 15px;
}

.share-form input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.current-shares {
    margin-bottom: 20px;
}

.shares-list {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.share-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
}

.share-item:last-child {
    border-bottom: none;
}

.share-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.share-username {
    font-weight: bold;
    color: #333;
}

.share-date {
    color: #777;
    font-size: 0.9em;
}

.share-permission {
    font-size: 0.8em;
    padding: 3px 8px;
    border-radius: 12px;
}

.share-permission.can-edit {
    background-color: #ff9800;
    color: white;
}

.share-permission.view-only {
    background-color: #9e9e9e;
    color: white;
}

.remove-btn {
    background-color: #f44336;
    color: white;
    padding: 5px 10px;
    font-size: 0.8em;
}

.remove-btn:hover {
    background-color: #d32f2f;
}

.share-link {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.link-container {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.link-container input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
}

.copy-btn {
    background-color: #2196f3;
    color: white;
}

.copy-btn:hover {
    background-color: #0b7dda;
}

.link-note {
    color: #777;
    font-size: 0.9em;
}

.back-link {
    margin-top: 20px;
}

.back-btn {
    background-color: #6e48aa;
    color: white;
}

.back-btn:hover {
    background-color: #5a3a8a;
}

/* View analysis page styles */
.view-analysis {
    padding: 20px 0;
}

.analysis-header {
    margin-bottom: 20px;
}

.analysis-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
}

.analysis-owner, .analysis-date {
    color: #777;
    font-size: 0.9em;
    margin: 0;
}

.analysis-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.input-text {
    margin-bottom: 20px;
}

.input-text pre {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: monospace;
    font-size: 0.9em;
    line-height: 1.5;
}

.result-section {
    margin-bottom: 25px;
}

.result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.result-item {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
}

.result-item h5 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
}

.result-item ul {
    margin: 0;
    padding-left: 20px;
}

.result-item li {
    margin-bottom: 5px;
}

.comments-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Flash messages */
.flash-messages {
    margin-bottom: 20px;
}

.flash-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.flash-message.success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.flash-message.danger {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

.flash-message.warning {
    background-color: #fcf8e3;
    color: #8a6d3b;
    border: 1px solid #faebcc;
}

.flash-message.info {
    background-color: #d9edf7;
    color: #31708f;
    border: 1px solid #bce8f1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .analysis-grid {
        grid-template-columns: 1fr;
    }
    
    .result-grid {
        grid-template-columns: 1fr;
    }
    
    .analysis-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .analysis-actions {
        flex-wrap: wrap;
    }
    
    .btn {
        flex: 1;
        min-width: 120px;
    }
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(90deg, #6e48aa, #9d50bb);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: linear-gradient(90deg, #5d3a99, #8c40aa);
    transform: translateY(-3px);
}

/* Floating Exit Button */
.floating-exit {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(100px);
    z-index: 1000;
}

.floating-exit.visible {
    opacity: 1;
    transform: translateY(0);
}

.floating-exit:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    color: white;
    text-decoration: none;
}

.floating-exit i {
    animation: door-swing 2s ease-in-out infinite;
}

@keyframes door-swing {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(10deg); }
}

/* Collapsible sections */
.collapsible {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #4a5568;
    cursor: pointer;
    padding: 20px 24px;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    font-size: 1.1em;
    font-weight: 600;
    border-radius: 16px;
    margin-bottom: 12px;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(10px);
}

.collapsible:after {
    content: '\002B';
    color: #667eea;
    font-weight: bold;
    float: right;
    margin-left: 5px;
    font-size: 18px;
    transition: transform 0.3s ease;
}

.active.collapsible:after {
    content: "\2212";
    transform: rotate(180deg);
}

.collapsible:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.collapsible-content {
    padding: 0 24px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 0 0 16px 16px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-top: none;
}

/* Results navigation */
.results-nav {
    position: sticky;
    top: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 16px 20px;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    z-index: 100;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.nav-link {
    padding: 10px 18px;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    text-decoration: none;
    border-radius: 25px;
    font-size: 0.9em;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.nav-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Tabs for results sections */
.tabs {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.tab-button {
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1em;
    color: #777;
    transition: all 0.3s;
}

.tab-button.active {
    color: #6e48aa;
    border-bottom: 2px solid #6e48aa;
}

.tab-button:hover {
    color: #6e48aa;
}

.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
}

/* Export Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #2c2c2c;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px 15px;
    border-bottom: 1px solid #444;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.close {
    color: #999;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s;
}

.close:hover {
    color: white;
    background-color: #444;
}

.modal-body {
    padding: 20px 25px;
}

.export-option-group {
    margin-bottom: 20px;
}

.export-option-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #ccc;
    font-size: 14px;
}

#filename {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #555;
    border-radius: 6px;
    background-color: #3c3c3c;
    color: white;
    font-size: 14px;
    box-sizing: border-box;
}

#filename:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

.format-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.format-option {
    padding: 15px 12px;
    border: 1px solid #555;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #3c3c3c;
}

.format-option:hover {
    border-color: #007AFF;
    background-color: #444;
}

.format-option.active {
    border-color: #007AFF;
    background-color: rgba(0, 122, 255, 0.1);
}

.format-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.format-name {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: white;
}

.format-desc {
    font-size: 12px;
    color: #999;
    line-height: 1.3;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 15px 25px 20px;
    border-top: 1px solid #444;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.cancel-btn {
    background-color: #555;
    color: white;
}

.cancel-btn:hover {
    background-color: #666;
}

.export-confirm-btn {
    background-color: #007AFF;
    color: white;
}

.export-confirm-btn:hover {
    background-color: #0056CC;
}

/* Responsive modal */
@media (max-width: 600px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .format-options {
        grid-template-columns: repeat(2, 1fr);
    }

    .modal-footer {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }
}
