/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    background: linear-gradient(135deg, #6e48aa, #9d50bb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #6e48aa;
}

.user-info {
    color: #6e48aa;
    font-weight: 500;
    padding: 8px 12px;
    background: rgba(110, 72, 170, 0.1);
    border-radius: 6px;
    font-size: 14px;
}

.logout-btn {
    background: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
}

.logout-btn:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
    color: white !important;
}

.dashboard-btn {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border-color: #28a745 !important;
    color: white !important;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.dashboard-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080) !important;
    border-color: #1e7e34 !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.dashboard-main-btn {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border-color: #28a745 !important;
    color: white !important;
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    animation: pulse-dashboard 2s infinite;
}

.dashboard-main-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080) !important;
    border-color: #1e7e34 !important;
    color: white !important;
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(40, 167, 69, 0.4);
}

@keyframes pulse-dashboard {
    0% { box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3); }
    50% { box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5); }
    100% { box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3); }
}

/* Floating Dashboard Button */
.floating-dashboard {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    opacity: 0;
    transform: translateY(100px);
    transition: all 0.3s ease;
}

.floating-dashboard.visible {
    opacity: 1;
    transform: translateY(0);
}

.floating-dashboard-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 12px 20px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
    animation: float 3s ease-in-out infinite;
}

.floating-dashboard-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(40, 167, 69, 0.4);
    color: white;
    text-decoration: none;
}

.floating-dashboard-btn i {
    font-size: 18px;
}

.floating-dashboard-btn span {
    font-size: 14px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #6e48aa, #9d50bb);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(110, 72, 170, 0.3);
}

.btn-outline {
    background: transparent;
    color: #6e48aa;
    border: 2px solid #6e48aa;
}

.btn-outline:hover {
    background: #6e48aa;
    color: white;
}

.btn-secondary {
    background: rgba(110, 72, 170, 0.1);
    color: #6e48aa;
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* Breadcrumb */
.breadcrumb-section {
    background: #f8f9fa;
    padding: 15px 0;
    margin-top: 70px;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.breadcrumb-item {
    color: #6e48aa;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.breadcrumb-item:hover {
    background: rgba(110, 72, 170, 0.1);
    text-decoration: none;
    color: #6e48aa;
}

.breadcrumb-separator {
    color: #999;
    font-weight: bold;
}

.breadcrumb-current {
    color: #666;
    font-weight: 500;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

/* Adjust hero padding when breadcrumb is present */
.breadcrumb-section + .hero {
    padding-top: 80px;
    min-height: calc(100vh - 70px);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
}

.gradient-text {
    background: linear-gradient(135deg, #6e48aa, #9d50bb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 60px;
}

.hero-stats {
    display: flex;
    gap: 40px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #6e48aa;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* Demo Card */
.demo-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform: rotate(3deg);
    transition: transform 0.3s ease;
}

.demo-card:hover {
    transform: rotate(0deg) scale(1.05);
}

.demo-header {
    background: linear-gradient(135deg, #6e48aa, #9d50bb);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.demo-dots {
    display: flex;
    gap: 6px;
}

.demo-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.demo-content {
    padding: 24px;
}

.demo-line {
    padding: 8px 0;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.highlight {
    color: #6e48aa;
    font-weight: 600;
}

/* Features Section */
.features {
    padding: 100px 0;
    background: white;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.section-header p {
    font-size: 1.25rem;
    color: #666;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.feature-card {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 24px;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Pricing Section */
.pricing {
    padding: 100px 0;
    background: #f8f9ff;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.pricing-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    position: relative;
    transition: transform 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-8px);
}

.pricing-card.featured {
    border: 2px solid #6e48aa;
    transform: scale(1.05);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #6e48aa, #9d50bb);
    color: white;
    padding: 8px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: 32px;
}

.pricing-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.price {
    font-size: 3rem;
    font-weight: 700;
    color: #6e48aa;
}

.price span {
    font-size: 1rem;
    color: #666;
}

.pricing-features {
    list-style: none;
    margin-bottom: 32px;
}

.pricing-features li {
    padding: 12px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.pricing-features i {
    color: #22c55e;
    width: 16px;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    padding: 24px 32px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 32px;
}

/* Auth Forms */
.auth-form {
    margin-bottom: 32px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #6e48aa;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.forgot-password {
    color: #6e48aa;
    text-decoration: none;
    font-size: 14px;
}

.auth-divider {
    text-align: center;
    margin: 32px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
}

.auth-divider span {
    background: white;
    padding: 0 16px;
    color: #666;
    font-size: 14px;
}

.social-auth {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 24px;
}

.btn-social {
    padding: 12px;
    border: 2px solid #e5e7eb;
    background: white;
    color: #333;
    justify-content: center;
}

.btn-google:hover { border-color: #db4437; color: #db4437; }
.btn-facebook:hover { border-color: #4267b2; color: #4267b2; }
.btn-github:hover { border-color: #333; color: #333; }

.auth-footer {
    text-align: center;
    color: #666;
    font-size: 14px;
}

.auth-footer a {
    color: #6e48aa;
    text-decoration: none;
    font-weight: 500;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 20px;
}

.footer-section a {
    display: block;
    color: #ccc;
    text-decoration: none;
    margin-bottom: 12px;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #6e48aa;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #333;
    color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: fixed;
        left: 0;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 20px 0;
        z-index: 1001;
    }

    .nav-menu.active {
        display: flex;
    }

    .hamburger {
        display: flex;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .social-auth {
        grid-template-columns: 1fr;
    }
}
