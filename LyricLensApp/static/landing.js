// Landing Page JavaScript

// Modal Functions
function openSignUpModal() {
    document.getElementById('signUpModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeSignUpModal() {
    document.getElementById('signUpModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

function openSignInModal() {
    document.getElementById('signInModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeSignInModal() {
    document.getElementById('signInModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

function switchToSignIn() {
    closeSignUpModal();
    openSignInModal();
}

function switchToSignUp() {
    closeSignInModal();
    openSignUpModal();
}

// Close modal when clicking outside
window.onclick = function(event) {
    const signUpModal = document.getElementById('signUpModal');
    const signInModal = document.getElementById('signInModal');
    
    if (event.target === signUpModal) {
        closeSignUpModal();
    }
    if (event.target === signInModal) {
        closeSignInModal();
    }
}

// Form Submissions
document.getElementById('signUpForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        email: formData.get('email'),
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    try {
        const response = await fetch('/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(data)
        });
        
        if (response.ok) {
            // Registration successful
            showNotification('Account created successfully! Please sign in.', 'success');
            closeSignUpModal();
            openSignInModal();
        } else {
            const errorText = await response.text();
            showNotification('Registration failed. Please try again.', 'error');
        }
    } catch (error) {
        showNotification('Network error. Please try again.', 'error');
    }
});

document.getElementById('signInForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    try {
        const response = await fetch('/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(data)
        });
        
        if (response.ok) {
            // Login successful
            showNotification('Welcome back!', 'success');
            closeSignInModal();
            // Redirect to main app
            window.location.href = '/';
        } else {
            showNotification('Invalid credentials. Please try again.', 'error');
        }
    } catch (error) {
        showNotification('Network error. Please try again.', 'error');
    }
});

// Social Authentication Functions
function signInWithGoogle() {
    showNotification('Google Sign-In coming soon!', 'info');
    // TODO: Implement Google OAuth
    // window.location.href = '/auth/google';
}

function signInWithFacebook() {
    showNotification('Facebook Sign-In coming soon!', 'info');
    // TODO: Implement Facebook OAuth
    // window.location.href = '/auth/facebook';
}

function signInWithGithub() {
    showNotification('GitHub Sign-In coming soon!', 'info');
    // TODO: Implement GitHub OAuth
    // window.location.href = '/auth/github';
}

// Logout Function
function logout() {
    // Show confirmation dialog
    if (confirm('Are you sure you want to log out?')) {
        fetch('/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => {
            if (response.ok) {
                showNotification('Logged out successfully!', 'success');
                // Reload the page to update the navigation
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('Logout failed', 'error');
            }
        })
        .catch(error => {
            showNotification('Network error', 'error');
        });
    }
}

// Quick Admin Login (for testing)
function quickAdminLogin(adminNumber) {
    const username = `admin${adminNumber}`;
    const password = `admin${adminNumber}`;

    fetch('/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            username: username,
            password: password
        })
    })
    .then(response => {
        if (response.ok) {
            showNotification(`Logged in as ${username}!`, 'success');
            closeSignInModal();
            // Reload the page to update the navigation
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification('Admin login failed', 'error');
        }
    })
    .catch(error => {
        showNotification('Network error', 'error');
    });
}

// Notification System
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 3000;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease;
        max-width: 400px;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Smooth scrolling for navigation links
function scrollToDemo() {
    document.getElementById('features').scrollIntoView({
        behavior: 'smooth'
    });
}

// Navbar scroll effect and floating dashboard button
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    const floatingDashboard = document.getElementById('floatingDashboard');

    // Navbar effect
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = 'none';
    }

    // Floating dashboard button effect
    if (floatingDashboard) {
        if (window.scrollY > 300) {
            floatingDashboard.classList.add('visible');
        } else {
            floatingDashboard.classList.remove('visible');
        }
    }
});

// Mobile menu toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

if (hamburger) {
    hamburger.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');
    });
}

// Add CSS for mobile menu
const mobileMenuCSS = `
    @media (max-width: 768px) {
        .nav-menu.active {
            display: flex;
            position: fixed;
            left: 0;
            top: 70px;
            flex-direction: column;
            background-color: white;
            width: 100%;
            text-align: center;
            transition: 0.3s;
            box-shadow: 0 10px 27px rgba(0,0,0,0.05);
            padding: 20px 0;
        }
        
        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }
        
        .hamburger.active span:nth-child(1) {
            transform: translateY(8px) rotate(45deg);
        }
        
        .hamburger.active span:nth-child(3) {
            transform: translateY(-8px) rotate(-45deg);
        }
    }
    
    .notification {
        animation: slideInRight 0.3s ease;
    }
    
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
`;

// Add the CSS to the page
const style = document.createElement('style');
style.textContent = mobileMenuCSS;
document.head.appendChild(style);

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add any initialization code here
    console.log('LyricLens+ Landing Page Loaded');
    
    // Add keyboard shortcuts for quick admin login (for testing)
    document.addEventListener('keydown', function(e) {
        // Ctrl+Alt+1 for admin1
        if (e.ctrlKey && e.altKey && e.key === '1') {
            e.preventDefault();
            quickAdminLogin(1);
        }
        // Ctrl+Alt+2 for admin2
        if (e.ctrlKey && e.altKey && e.key === '2') {
            e.preventDefault();
            quickAdminLogin(2);
        }
    });
});

// Add demo data for testing
const demoLyrics = [
    "I'm walking down this empty street tonight\nThinking about you and the way you used to smile\nMy heart is racing with every step I take\nWondering if you're thinking of me too",
    
    "Started from the bottom now we here\nMoney coming in but the pain still clear\nTrust issues got me feeling so alone\nSuccess don't mean nothing when you're on your own",
    
    "Whisper in the dark, tell me all your fears\nBlack and white dreams, drowning in my tears\nDon't you dare look at me that way\nI'm not the same girl from yesterday"
];

// Function to fill demo lyrics (for testing)
function fillDemoLyrics(index = 0) {
    const textarea = document.querySelector('textarea');
    if (textarea && demoLyrics[index]) {
        textarea.value = demoLyrics[index];
    }
}
