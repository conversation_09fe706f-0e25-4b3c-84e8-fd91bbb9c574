<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>LyricLens+ with ESVAF Analyzer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>LyricLens+ with ESVAF Analyzer</h1>
            <p>Created by <PERSON><PERSON><PERSON></p>
        </header>

        <div class="mode-selector">
            <button id="single-mode-btn" class="mode-btn active" onclick="switchMode('single')">Single Analysis</button>
            <button id="compare-mode-btn" class="mode-btn" onclick="switchMode('compare')">Compare Lyrics</button>
        </div>

        <form id="single-form" method="POST" enctype="multipart/form-data">
            <div>
                <label>Upload a file (image, PDF, text):</label>
                <input type="file" name="file" accept=".png,.jpg,.jpeg,.pdf,.txt">
            </div>
            <div>
                <label>Or paste text:</label>
                <textarea name="text" rows="5" cols="50" placeholder="Paste your story or song here..."></textarea>
            </div>
            <button type="submit">Analyze</button>
            <a href="https://paypal.me/benjimusic" target="_blank" class="donation-btn">Support with a Donation</a>
        </form>

        <form id="compare-form" method="POST" action="/compare" enctype="multipart/form-data" style="display: none;">
            <div class="comparison-container">
                <div class="comparison-column">
                    <h3>Lyrics A</h3>
                    <div>
                        <label>Upload a file:</label>
                        <input type="file" name="file_a" accept=".png,.jpg,.jpeg,.pdf,.txt">
                    </div>
                    <div>
                        <label>Or paste text:</label>
                        <textarea name="text_a" rows="5" cols="50" placeholder="Paste your first set of lyrics here..."></textarea>
                    </div>
                </div>
                <div class="comparison-column">
                    <h3>Lyrics B</h3>
                    <div>
                        <label>Upload a file:</label>
                        <input type="file" name="file_b" accept=".png,.jpg,.jpeg,.pdf,.txt">
                    </div>
                    <div>
                        <label>Or paste text:</label>
                        <textarea name="text_b" rows="5" cols="50" placeholder="Paste your second set of lyrics here..."></textarea>
                    </div>
                </div>
            </div>
            <button type="submit">Compare Lyrics</button>
            <a href="https://paypal.me/benjimusic" target="_blank" class="donation-btn">Support with a Donation</a>
        </form>

        {% if error %}
            <p style="color: red;">{{ error }}</p>
        {% endif %}

        {% if analysis %}
            <div class="results">
                <h2>Analysis Results</h2>
                
                <!-- Results Navigation -->
                <div class="results-nav">
                    <a href="#input-text" class="nav-link">Input Text</a>
                    <a href="#external-details" class="nav-link">External Details</a>
                    <a href="#internal-details" class="nav-link">Internal Details</a>
                    <a href="#esvaf-elements" class="nav-link">ESVAF Elements</a>
                    <a href="#sentiment" class="nav-link">Sentiment</a>
                    <a href="#rhyme" class="nav-link">Rhyme Analysis</a>
                    <a href="#genre" class="nav-link">Genre</a>
                    <a href="#storytelling" class="nav-link">Storytelling</a>
                    <a href="#artist-detection" class="nav-link">Artist Detection</a>
                    <a href="#similar-works" class="nav-link">Similar Works</a>
                </div>

                <!-- Input Text Section -->
                <button class="collapsible" id="input-text">Input Text</button>
                <div class="collapsible-content">
                    <pre>{{ input_text }}</pre>
                </div>
                
                <!-- External Details Section -->
                <button class="collapsible" id="external-details">External Details</button>
                <div class="collapsible-content">
                    <ul>
                        <li><strong>Location:</strong>
                            <ul>
                                {% for item in analysis.External_Details.Location %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Actions:</strong>
                            <ul>
                                {% for item in analysis.External_Details.Actions %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Nouns (5¢ each):</strong>
                            <ul>
                                {% for item in analysis.External_Details.Nouns %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Verbs (50¢ each):</strong>
                            <ul>
                                {% for item in analysis.External_Details.Verbs %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Adjectives (5¢ each):</strong>
                            <ul>
                                {% for item in analysis.External_Details.Adjectives %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                    </ul>
                </div>
                
                <!-- Internal Details Section -->
                <button class="collapsible" id="internal-details">Internal Details</button>
                <div class="collapsible-content">
                    <ul>
                        <li><strong>Thoughts:</strong>
                            <ul>
                                {% for item in analysis.Internal_Details.Thoughts %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Emotions:</strong>
                            <ul>
                                {% for item in analysis.Internal_Details.Emotions %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Dialogue:</strong>
                            <ul>
                                {% for item in analysis.Internal_Details.Dialogue %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Resolution/Payoff:</strong>
                            <ul>
                                {% for item in analysis.Internal_Details.Resolution_Payoff %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                    </ul>
                </div>

                <!-- ESVAF Elements Section -->
                <button class="collapsible" id="esvaf-elements">Additional ESVAF Elements</button>
                <div class="collapsible-content">
                    <ul>
                        <li><strong>Conflict/Tension:</strong>
                            <ul>
                                {% for item in analysis.Conflict_Tension %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Audience Hook:</strong>
                            <ul>
                                {% for item in analysis.Audience_Hook %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Metaphors (50¢ each):</strong>
                            <ul>
                                {% for item in analysis.Metaphors %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Rhythm/Meter (5¢ per line):</strong>
                            <ul>
                                {% for item in analysis.Rhythm_Meter %}
                                    <li>{{ item }}</li>
                                {% else %}
                                    <li>None detected</li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li><strong>Total Value:</strong> {{ analysis.Total_Value }} cents (${{ "%.2f"|format(analysis.Total_Value / 100) }})</li>
                    </ul>
                </div>

                <!-- Sentiment Analysis Section -->
                <button class="collapsible" id="sentiment">Sentiment Analysis</button>
                <div class="collapsible-content">
                    <ul>
                        <li><strong>Overall Sentiment:</strong> {{ analysis.Sentiment_Analysis.sentiment }}</li>
                        <li><strong>Polarity Score:</strong> {{ analysis.Sentiment_Analysis.polarity|round(2) }} 
                            <span class="tooltip">(Range: -1 to 1, where -1 is negative, 0 is neutral, 1 is positive)
                                <span class="tooltiptext">Higher values indicate more positive sentiment</span>
                            </span>
                        </li>
                        <li><strong>Subjectivity Score:</strong> {{ analysis.Sentiment_Analysis.subjectivity|round(2) }}
                            <span class="tooltip">(Range: 0 to 1)
                                <span class="tooltiptext">Higher values indicate more subjective/opinionated content</span>
                            </span>
                        </li>
                    </ul>
                </div>

                <!-- Rhyme Analysis Section -->
                <button class="collapsible" id="rhyme">Rhyme Pattern Analysis</button>
                <div class="collapsible-content">
                    <div class="rhyme-analysis">
                        <p><strong>Rhyme Density:</strong> {{ (analysis.Rhyme_Analysis.rhyme_density * 100)|round(1) }}% of lines contain rhymes</p>
                        
                        <h4>Annotated Lines with Rhyme Scheme:</h4>
                        <ul class="rhyme-lines">
                            {% for line in analysis.Rhyme_Analysis.annotated_lines %}
                                <li>{{ line }}</li>
                            {% endfor %}
                        </ul>
                        
                        <div class="rhyme-legend">
                            <p><strong>Rhyme Scheme Legend:</strong></p>
                            <ul>
                                <li>A, B, C, etc. - Different rhyme groups</li>
                                <li>Lines with the same letter rhyme with each other</li>
                                <li>"No rhyme detected" - Line doesn't rhyme with others</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Genre Classification Section -->
                <button class="collapsible" id="genre">Genre Classification</button>
                <div class="collapsible-content">
                    <div class="genre-analysis">
                        <p><strong>Potential Genres:</strong></p>
                        <div class="genre-bars">
                            {% for genre, percentage in analysis.Genre_Classification.top_genres %}
                                {% if percentage > 0 %}
                                    <div class="genre-bar-container">
                                        <div class="genre-label">{{ genre }}</div>
                                        <div class="genre-bar">
                                            <div class="genre-fill" style="width: {{ percentage }}%"></div>
                                        </div>
                                        <div class="genre-percentage">{{ percentage|round(1) }}%</div>
                                    </div>
                                {% endif %}
                            {% else %}
                                <p>No genre indicators found</p>
                            {% endfor %}
                        </div>
                        
                        <div class="genre-note">
                            <p><strong>Note:</strong> Genre classification is based on keyword analysis and is meant as a suggestion only. 
                            Many songs cross multiple genres or create new ones!</p>
                        </div>
                    </div>
                </div>

                <!-- Storytelling Analysis Section -->
                <button class="collapsible" id="storytelling">Storytelling Analysis</button>
                <div class="collapsible-content">
                    <div class="storytelling-analysis">
                        <div class="storytelling-overview">
                            <p><strong>Storytelling Strength:</strong>
                                <span class="storytelling-strength {{ analysis.Storytelling_Analysis.storytelling_strength|lower }}">
                                    {{ analysis.Storytelling_Analysis.storytelling_strength }}
                                </span>
                            </p>
                            <p><strong>Storytelling Density:</strong> {{ (analysis.Storytelling_Analysis.storytelling_density * 100)|round(1) }}% of lines contain storytelling elements</p>
                            <p><strong>Total Storytelling Elements:</strong> {{ analysis.Storytelling_Analysis.storytelling_score }}</p>
                        </div>

                        <div class="storytelling-elements">
                            <div class="storytelling-element">
                                <h4>🗺️ Location (Where you are)</h4>
                                <p class="element-description">Physical places and settings that help listeners visualize the scene</p>
                                <ul class="storytelling-list">
                                    {% for item in analysis.Storytelling_Analysis.Location %}
                                        <li>{{ item }}</li>
                                    {% else %}
                                        <li class="no-elements">No location elements detected</li>
                                    {% endfor %}
                                </ul>
                            </div>

                            <div class="storytelling-element">
                                <h4>🏃 Actions (What you're doing)</h4>
                                <p class="element-description">Specific actions and movements that bring forward momentum to the story</p>
                                <ul class="storytelling-list">
                                    {% for item in analysis.Storytelling_Analysis.Actions %}
                                        <li>{{ item }}</li>
                                    {% else %}
                                        <li class="no-elements">No action elements detected</li>
                                    {% endfor %}
                                </ul>
                            </div>

                            <div class="storytelling-element">
                                <h4>💭 Thoughts (What you're thinking)</h4>
                                <p class="element-description">Internal mental processes, hopes, fears, and raw unfiltered thoughts</p>
                                <ul class="storytelling-list">
                                    {% for item in analysis.Storytelling_Analysis.Thoughts %}
                                        <li>{{ item }}</li>
                                    {% else %}
                                        <li class="no-elements">No thought elements detected</li>
                                    {% endfor %}
                                </ul>
                            </div>

                            <div class="storytelling-element">
                                <h4>❤️ Emotions (What you're feeling)</h4>
                                <p class="element-description">Physical manifestations of emotions shown through body language and reactions</p>
                                <ul class="storytelling-list">
                                    {% for item in analysis.Storytelling_Analysis.Emotions %}
                                        <li>{{ item }}</li>
                                    {% else %}
                                        <li class="no-elements">No emotion elements detected</li>
                                    {% endfor %}
                                </ul>
                            </div>

                            <div class="storytelling-element">
                                <h4>💬 Dialogue (What you're hearing)</h4>
                                <p class="element-description">Direct speech and conversations that make the story more engaging</p>
                                <ul class="storytelling-list">
                                    {% for item in analysis.Storytelling_Analysis.Dialogue %}
                                        <li>{{ item }}</li>
                                    {% else %}
                                        <li class="no-elements">No dialogue elements detected</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>

                        <div class="storytelling-tips">
                            <h4>💡 Storytelling Tips</h4>
                            <ul>
                                <li><strong>Location:</strong> Start with where you are physically - "I'm sitting in my car..." or "Standing in the kitchen..."</li>
                                <li><strong>Actions:</strong> Use specific verbs - "walking", "grabbing", "staring" instead of general descriptions</li>
                                <li><strong>Thoughts:</strong> Share raw, unfiltered thoughts - "I thought, oh man, this is bad" instead of "I was concerned"</li>
                                <li><strong>Emotions:</strong> Show don't tell - "my heart was racing" instead of "I was nervous"</li>
                                <li><strong>Dialogue:</strong> Use exact words - "She looked at me and said, 'What are you doing?'" instead of "She questioned me"</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- AI Artist Detection Section -->
                {% if analysis.AI_Artist_Detection %}
                <button class="collapsible" id="artist-detection">🎤 AI Artist Detection</button>
                <div class="collapsible-content">
                    <div class="artist-detection-analysis">
                        <div class="artist-detection-header">
                            <h3>🤖 AI-Powered Artist Style Analysis</h3>
                            <p>Our AI analyzed your lyrics and identified which artists your style most resembles:</p>
                        </div>

                        <div class="artist-matches">
                            <div class="ai-response-content">
                                <pre>{{ analysis.AI_Artist_Detection.choices[0].message.content if analysis.AI_Artist_Detection.choices else 'AI analysis in progress...' }}</pre>
                            </div>
                        </div>

                        <div class="artist-detection-tips">
                            <h4>💡 How Artist Detection Works</h4>
                            <ul>
                                <li><strong>Lyrical Style:</strong> Analyzes word choice, themes, and emotional expression</li>
                                <li><strong>Narrative Approach:</strong> Examines storytelling style and perspective</li>
                                <li><strong>Vocabulary Analysis:</strong> Considers complexity, slang, and cultural references</li>
                                <li><strong>Emotional Patterns:</strong> Identifies how feelings are conveyed</li>
                                <li><strong>Genre Indicators:</strong> Detects musical style characteristics</li>
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Similar Works Section -->
                <button class="collapsible" id="similar-works">Similar Historical Works</button>
                <div class="collapsible-content">
                    <div class="similar-works">
                        {% if analysis.Similar_Works %}
                            <div class="similar-works-grid">
                                {% for work in analysis.Similar_Works %}
                                    <div class="similar-work-card">
                                        <div class="work-type-badge {{ work.type }}">{{ work.type|capitalize }}</div>
                                        <h4>{{ work.title }} ({{ work.year }})</h4>
                                        <p class="work-creator">By {{ work.creator }}</p>
                                        <p class="work-reason">{{ work.reason }}</p>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p>No similar works found for this content.</p>
                        {% endif %}
                        
                        <div class="similar-works-note">
                            <p><strong>Note:</strong> Suggestions are based on genre, sentiment, and rhyme patterns in your lyrics. 
                            These historical works might provide inspiration or context for your writing.</p>
                        </div>
                    </div>
                </div>

                <!-- Export Section -->
                <div class="export-section">
                    <h3>Export Your Analysis</h3>
                    <p>Save your analysis results in your preferred format for future reference or sharing.</p>
                    <div class="export-buttons">
                        <button onclick="printResults()" class="print-btn">Print Analysis</button>
                        <button onclick="openExportModal()" class="export-btn main-export-btn">Export Analysis</button>
                    </div>
                </div>
            </div>

            <!-- Back to Top Button -->
            <a href="#" class="back-to-top" id="back-to-top">↑</a>

            <!-- Floating Exit Button -->
            <a href="/landing" class="floating-exit" id="floating-exit" title="Exit to Dashboard">
                <i class="fas fa-door-open"></i>
            </a>
        {% endif %}

        <!-- Export Modal -->
        <div id="exportModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Export Analysis</h3>
                    <span class="close" onclick="closeExportModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="export-option-group">
                        <label for="filename">Export As:</label>
                        <input type="text" id="filename" value="lyriclens_analysis" placeholder="Enter filename">
                        <div id="artistDetectionInfo" style="margin-top: 8px; font-size: 0.9em; color: #666; display: none;">
                            🎤 <span id="detectedArtist"></span> style detected - filename auto-generated
                        </div>
                    </div>

                    <div class="export-option-group">
                        <label>Format:</label>
                        <div class="format-options">
                            <div class="format-option" onclick="selectFormat('pdf')">
                                <div class="format-icon">📄</div>
                                <div class="format-name">PDF</div>
                                <div class="format-desc">Portable Document Format</div>
                            </div>
                            <div class="format-option" onclick="selectFormat('csv')">
                                <div class="format-icon">📊</div>
                                <div class="format-name">CSV</div>
                                <div class="format-desc">Comma Separated Values</div>
                            </div>
                            <div class="format-option" onclick="selectFormat('json')">
                                <div class="format-icon">🔧</div>
                                <div class="format-name">JSON</div>
                                <div class="format-desc">JavaScript Object Notation</div>
                            </div>
                            <div class="format-option" onclick="selectFormat('txt')">
                                <div class="format-icon">📝</div>
                                <div class="format-name">Text</div>
                                <div class="format-desc">Plain Text File</div>
                            </div>
                            <div class="format-option" onclick="selectFormat('docx')">
                                <div class="format-icon">📄</div>
                                <div class="format-name">Word</div>
                                <div class="format-desc">Microsoft Word Document</div>
                            </div>
                            <div class="format-option" onclick="selectFormat('png')">
                                <div class="format-icon">🖼️</div>
                                <div class="format-name">PNG</div>
                                <div class="format-desc">Portable Network Graphics</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="closeExportModal()" class="btn cancel-btn">Cancel</button>
                    <button onclick="confirmExport()" class="btn export-confirm-btn">Export</button>
                </div>
            </div>
        </div>

        {% if analysis and can_save %}
            <div class="save-analysis-form">
                <h3>Save This Analysis</h3>
                <form action="{{ url_for('save_analysis') }}" method="POST">
                    <div class="form-group">
                        <label for="title">Title</label>
                        <input type="text" id="title" name="title" required placeholder="Enter a title for this analysis">
                    </div>
                    <div class="form-group checkbox">
                        <input type="checkbox" id="public" name="public">
                        <label for="public">Make this analysis public</label>
                    </div>
                    <input type="hidden" name="input_text" value="{{ input_text }}">
                    <input type="hidden" name="analysis_data" value="{{ analysis|tojson }}">
                    <button type="submit" class="btn save-btn">Save Analysis</button>
                </form>
            </div>
        {% endif %}
    </div>
    <footer>
        <p>Created By A A Mupemhi for Benjamin Music 2025</p>
    </footer>

    <script>
        function printResults() {
            const results = document.querySelector('.results');
            const printWindow = window.open('', '', 'width=800,height=600');
            printWindow.document.write('<html><head><title>LyricLens+ Analysis</title>');
            printWindow.document.write('<style>body{font-family:Arial,sans-serif;}h2,h3,h4{color:#333;}ul{list-style-type:none;}ul ul{list-style-type:disc;padding-left:20px;}li{margin:5px 0;}</style>');
            printWindow.document.write('</head><body>');
            printWindow.document.write(results.innerHTML);
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
        }
        
        function exportAnalysis(format) {
            // Create a form to submit the analysis data
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/export/' + format;
            
            // Add the analysis data as a hidden field
            const analysisData = document.createElement('input');
            analysisData.type = 'hidden';
            analysisData.name = 'analysis_data';
            {% if analysis %}
            analysisData.value = JSON.stringify({{ analysis|tojson }});
            {% else %}
            analysisData.value = '';
            {% endif %}
            form.appendChild(analysisData);
            
            // Add the input text as a hidden field
            const inputText = document.createElement('input');
            inputText.type = 'hidden';
            inputText.name = 'input_text';
            {% if input_text %}
            inputText.value = {{ input_text|tojson }};
            {% else %}
            inputText.value = '';
            {% endif %}
            form.appendChild(inputText);
            
            // Submit the form
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function switchMode(mode) {
            if (mode === 'single') {
                document.getElementById('single-form').style.display = 'block';
                document.getElementById('compare-form').style.display = 'none';
                document.getElementById('single-mode-btn').classList.add('active');
                document.getElementById('compare-mode-btn').classList.remove('active');
            } else {
                document.getElementById('single-form').style.display = 'none';
                document.getElementById('compare-form').style.display = 'block';
                document.getElementById('single-mode-btn').classList.remove('active');
                document.getElementById('compare-mode-btn').classList.add('active');
            }
        }

        // Export Modal Functions
        let selectedFormat = 'pdf';

        function openExportModal() {
            document.getElementById('exportModal').style.display = 'block';
            // Select PDF by default
            selectFormat('pdf');
            // Auto-generate filename based on AI detection
            updateFilenameFromAI();
        }

        function updateFilenameFromAI() {
            const filenameInput = document.getElementById('filename');
            const artistInfo = document.getElementById('artistDetectionInfo');
            const detectedArtistSpan = document.getElementById('detectedArtist');
            let suggestedName = 'lyriclens_analysis';
            let detectedArtist = null;

            // Check if AI artist detection is available
            {% if analysis and analysis.AI_Artist_Detection %}
                try {
                    const aiResponse = {{ analysis.AI_Artist_Detection|tojson }};
                    if (aiResponse && aiResponse.choices && aiResponse.choices[0]) {
                        const content = aiResponse.choices[0].message.content;

                        // Try to extract artist name from AI response
                        const artistMatch = content.match(/"artist":\s*"([^"]+)"/);
                        if (artistMatch && artistMatch[1]) {
                            detectedArtist = artistMatch[1];
                            const artistName = artistMatch[1].toLowerCase()
                                .replace(/[^a-z0-9\s]/g, '') // Remove special characters
                                .replace(/\s+/g, '_'); // Replace spaces with underscores
                            suggestedName = `${artistName}_style_analysis`;
                        }
                    }
                } catch (e) {
                    console.log('Could not parse AI artist detection for filename');
                }
            {% endif %}

            // Also check for detected artist in regular analysis
            {% if analysis and analysis.Similar_Works %}
                if (suggestedName === 'lyriclens_analysis') {
                    const similarWorks = {{ analysis.Similar_Works|tojson }};
                    if (similarWorks && similarWorks.length > 0) {
                        const firstSong = similarWorks[0];
                        if (firstSong.creator) {
                            detectedArtist = firstSong.creator;
                            const artistName = firstSong.creator.toLowerCase()
                                .replace(/[^a-z0-9\s]/g, '')
                                .replace(/\s+/g, '_');
                            suggestedName = `${artistName}_style_analysis`;
                        }
                    }
                }
            {% endif %}

            filenameInput.value = suggestedName;

            // Show artist detection info if artist was detected
            if (detectedArtist) {
                detectedArtistSpan.textContent = detectedArtist;
                artistInfo.style.display = 'block';
            } else {
                artistInfo.style.display = 'none';
            }
        }

        function closeExportModal() {
            document.getElementById('exportModal').style.display = 'none';
        }

        function selectFormat(format) {
            selectedFormat = format;
            // Remove active class from all format options
            document.querySelectorAll('.format-option').forEach(option => {
                option.classList.remove('active');
            });
            // Add active class to selected format
            event.target.closest('.format-option').classList.add('active');
        }

        function confirmExport() {
            const filename = document.getElementById('filename').value || 'lyriclens_analysis';
            closeExportModal();
            exportAnalysis(selectedFormat);
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('exportModal');
            if (event.target == modal) {
                closeExportModal();
            }
        }
    </script>
    <script>
        // Collapsible sections
        document.addEventListener('DOMContentLoaded', function() {
            var coll = document.getElementsByClassName("collapsible");
            for (var i = 0; i < coll.length; i++) {
                coll[i].addEventListener("click", function() {
                    this.classList.toggle("active");
                    var content = this.nextElementSibling;
                    if (content.style.maxHeight) {
                        content.style.maxHeight = null;
                    } else {
                        content.style.maxHeight = content.scrollHeight + "px";
                    }
                });
            }
            
            // Back to top button and floating exit button
            var backToTopButton = document.getElementById("back-to-top");
            var floatingExitButton = document.getElementById("floating-exit");

            window.onscroll = function() {
                if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
                    backToTopButton.classList.add("visible");
                    if (floatingExitButton) {
                        floatingExitButton.classList.add("visible");
                    }
                } else {
                    backToTopButton.classList.remove("visible");
                    if (floatingExitButton) {
                        floatingExitButton.classList.remove("visible");
                    }
                }
            };
            
            backToTopButton.addEventListener("click", function(e) {
                e.preventDefault();
                document.body.scrollTop = 0; // For Safari
                document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
            });
            
            // Open the first section by default
            if (coll.length > 0) {
                coll[0].click();
            }
            
            // Smooth scrolling for navigation links
            document.querySelectorAll('.nav-link').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    
                    if (targetElement) {
                        // Close all sections first
                        for (var i = 0; i < coll.length; i++) {
                            var content = coll[i].nextElementSibling;
                            content.style.maxHeight = null;
                            coll[i].classList.remove("active");
                        }
                        
                        // Open the target section
                        targetElement.classList.add("active");
                        var content = targetElement.nextElementSibling;
                        content.style.maxHeight = content.scrollHeight + "px";
                        
                        // Scroll to the section
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
