{% extends "base.html" %}

{% block title %}{{ analysis.title }} - LyricLens+{% endblock %}

{% block content %}
<div class="view-analysis">
    <div class="analysis-header">
        <h2>{{ analysis.title }}</h2>
        <div class="analysis-meta">
            <p class="analysis-owner">By: {{ analysis.owner.username }}</p>
            <p class="analysis-date">Created: {{ analysis.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
            <div class="analysis-badges">
                {% if analysis.public %}
                    <span class="badge public">Public</span>
                {% else %}
                    <span class="badge private">Private</span>
                {% endif %}
                
                {% if current_user.is_authenticated and analysis.user_id != current_user.id %}
                    {% for share in analysis.shares %}
                        {% if share.shared_with_id == current_user.id %}
                            {% if share.can_edit %}
                                <span class="badge can-edit">Can Edit</span>
                            {% else %}
                                <span class="badge view-only">View Only</span>
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        
        <div class="analysis-actions">
            {% if current_user.is_authenticated and analysis.user_id == current_user.id %}
                <a href="{{ url_for('share_analysis', analysis_id=analysis.id) }}" class="btn share-btn">Share</a>
            {% endif %}
            <button onclick="printResults()" class="btn print-btn">Print</button>
            <button onclick="exportAnalysis('pdf')" class="btn export-btn">Export PDF</button>
        </div>
    </div>
    
    <div class="analysis-content">
        <div class="input-text">
            <h3>Input Text:</h3>
            <pre>{{ input_text }}</pre>
        </div>
        
        <div class="analysis-results">
            <h3>Analysis Results:</h3>
            
            <!-- Display analysis results similar to index.html -->
            <!-- This is a simplified version, you can expand it as needed -->
            
            <div class="result-section">
                <h4>External Details:</h4>
                <div class="result-grid">
                    <div class="result-item">
                        <h5>Location:</h5>
                        <ul>
                            {% for item in analysis_result.External_Details.Location %}
                                <li>{{ item }}</li>
                            {% else %}
                                <li>None detected</li>
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <div class="result-item">
                        <h5>Actions:</h5>
                        <ul>
                            {% for item in analysis_result.External_Details.Actions %}
                                <li>{{ item }}</li>
                            {% else %}
                                <li>None detected</li>
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <!-- Add more external details as needed -->
                </div>
            </div>
            
            <div class="result-section">
                <h4>Internal Details:</h4>
                <div class="result-grid">
                    <div class="result-item">
                        <h5>Thoughts:</h5>
                        <ul>
                            {% for item in analysis_result.Internal_Details.Thoughts %}
                                <li>{{ item }}</li>
                            {% else %}
                                <li>None detected</li>
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <div class="result-item">
                        <h5>Emotions:</h5>
                        <ul>
                            {% for item in analysis_result.Internal_Details.Emotions %}
                                <li>{{ item }}</li>
                            {% else %}
                                <li>None detected</li>
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <!-- Add more internal details as needed -->
                </div>
            </div>
            
            <!-- Add more analysis sections as needed -->
            
            <div class="result-section">
                <h4>Total Value:</h4>
                <p>{{ analysis_result.Total_Value }} cents (${{ "%.2f"|format(analysis_result.Total_Value / 100) }})</p>
            </div>
        </div>
        
        {% if current_user.is_authenticated %}
            <div class="comments-section">
                <h3>Comments</h3>
                <!-- Add comments functionality here if desired -->
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function printResults() {
        const results = document.querySelector('.analysis-content');
        const printWindow = window.open('', '', 'width=800,height=600');
        printWindow.document.write('<html><head><title>{{ analysis.title }} - LyricLens+</title>');
        printWindow.document.write('<style>body{font-family:Arial,sans-serif;}h2,h3,h4,h5{color:#333;}ul{list-style-type:disc;padding-left:20px;}li{margin:5px 0;}</style>');
        printWindow.document.write('</head><body>');
        printWindow.document.write('<h2>{{ analysis.title }}</h2>');
        printWindow.document.write(results.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.print();
    }
    
    function exportAnalysis(format) {
        // Create a form to submit the analysis data
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/export/' + format;
        
        // Add the analysis data as a hidden field
        const analysisData = document.createElement('input');
        analysisData.type = 'hidden';
        analysisData.name = 'analysis_data';
        analysisData.value = JSON.stringify({{ analysis_result|tojson }});
        form.appendChild(analysisData);
        
        // Add the input text as a hidden field
        const inputText = document.createElement('input');
        inputText.type = 'hidden';
        inputText.name = 'input_text';
        inputText.value = {{ input_text|tojson }};
        form.appendChild(inputText);
        
        // Submit the form
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }
</script>
{% endblock %}