<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}LyricLens+ with ESVAF Analyzer{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container">
        <header>
            <h1>LyricLens+ with ESVAF Analyzer</h1>
            <p>Created by <PERSON><PERSON><PERSON></p>
            <nav class="main-nav">
                <ul>
                    <li><a href="{{ url_for('landing') }}">Landing</a></li>
                    <li><a href="{{ url_for('index') }}">Analyzer</a></li>
                    <li><a href="{{ url_for('api_docs') }}">API Docs</a></li>
                    {% if current_user.is_authenticated %}
                        <li><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                        <li><a href="{{ url_for('logout') }}">Logout ({{ current_user.username }})</a></li>
                    {% else %}
                        <li><a href="{{ url_for('login') }}">Login</a></li>
                        <li><a href="{{ url_for('register') }}">Register</a></li>
                    {% endif %}
                </ul>
            </nav>
        </header>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer>
        <p>Created By A A Mupemhi for Benjamin Music 2025</p>
    </footer>

    {% block scripts %}{% endblock %}
</body>
</html>