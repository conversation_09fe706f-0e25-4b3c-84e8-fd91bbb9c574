{% extends "base.html" %}

{% block title %}Dashboard - LyricLens+{% endblock %}

{% block content %}
<div class="dashboard">
    <h2>Your Dashboard</h2>
    
    <div class="dashboard-section">
        <h3>Your Analyses</h3>
        {% if user_analyses %}
            <div class="analysis-grid">
                {% for analysis in user_analyses %}
                    <div class="analysis-card">
                        <h4>{{ analysis.title }}</h4>
                        <p class="analysis-date">Created: {{ analysis.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        <p class="analysis-preview">{{ analysis.text[:100] }}{% if analysis.text|length > 100 %}...{% endif %}</p>
                        <div class="analysis-badges">
                            {% if analysis.public %}
                                <span class="badge public">Public</span>
                            {% else %}
                                <span class="badge private">Private</span>
                            {% endif %}
                            
                            {% set share_count = analysis.shares|length %}
                            {% if share_count > 0 %}
                                <span class="badge shared">Shared ({{ share_count }})</span>
                            {% endif %}
                        </div>
                        <div class="analysis-actions">
                            <a href="{{ url_for('view_analysis', analysis_id=analysis.id) }}" class="btn view-btn">View</a>
                            <a href="{{ url_for('share_analysis', analysis_id=analysis.id) }}" class="btn share-btn">Share</a>
                            <button class="btn delete-btn" onclick="confirmDelete({{ analysis.id }})">Delete</button>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="empty-state">You haven't created any analyses yet. <a href="{{ url_for('index') }}">Create your first analysis</a>.</p>
        {% endif %}
    </div>
    
    <div class="dashboard-section">
        <h3>Shared With You</h3>
        {% if shared_analyses %}
            <div class="analysis-grid">
                {% for analysis in shared_analyses %}
                    <div class="analysis-card shared-card">
                        <h4>{{ analysis.title }}</h4>
                        <p class="analysis-date">Created: {{ analysis.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        <p class="analysis-owner">Owner: {{ analysis.owner.username }}</p>
                        <p class="analysis-preview">{{ analysis.text[:100] }}{% if analysis.text|length > 100 %}...{% endif %}</p>
                        <div class="analysis-badges">
                            {% for share in analysis.shares %}
                                {% if share.shared_with_id == current_user.id %}
                                    {% if share.can_edit %}
                                        <span class="badge can-edit">Can Edit</span>
                                    {% else %}
                                        <span class="badge view-only">View Only</span>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </div>
                        <div class="analysis-actions">
                            <a href="{{ url_for('view_analysis', analysis_id=analysis.id) }}" class="btn view-btn">View</a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="empty-state">No analyses have been shared with you yet.</p>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="modal">
    <div class="modal-content">
        <h3>Confirm Deletion</h3>
        <p>Are you sure you want to delete this analysis? This action cannot be undone.</p>
        <div class="modal-actions">
            <form id="delete-form" method="POST">
                <button type="button" class="btn cancel-btn" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn delete-btn">Delete</button>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    function confirmDelete(analysisId) {
        const modal = document.getElementById('delete-modal');
        const form = document.getElementById('delete-form');
        form.action = "{{ url_for('delete_analysis', analysis_id=0) }}".replace('0', analysisId);
        modal.style.display = 'flex';
    }
    
    function closeModal() {
        const modal = document.getElementById('delete-modal');
        modal.style.display = 'none';
    }
    
    // Close modal when clicking outside of it
    window.onclick = function(event) {
        const modal = document.getElementById('delete-modal');
        if (event.target == modal) {
            closeModal();
        }
    }
</script>
{% endblock %}
