<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LyricLens+ | AI-Powered Lyric Analysis Platform</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='landing.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>🎵 LyricLens+</h2>
            </div>
            <div class="nav-menu">
                <a href="#features" class="nav-link">Features</a>
                <a href="#pricing" class="nav-link">Pricing</a>
                <a href="#about" class="nav-link">About</a>
                {% if current_user.is_authenticated %}
                    <a href="/" class="btn btn-primary dashboard-btn">
                        <i class="fas fa-tachometer-alt"></i> Back to Dashboard
                    </a>
                    <span class="user-info">Welcome, {{ current_user.username }}!</span>
                    <button onclick="logout()" class="btn btn-outline logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Log Out
                    </button>
                {% else %}
                    <button onclick="openSignInModal()" class="btn btn-outline">Sign In</button>
                    <button onclick="openSignUpModal()" class="btn btn-primary">Get Started</button>
                {% endif %}
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb for logged-in users -->
    {% if current_user.is_authenticated %}
    <div class="breadcrumb-section">
        <div class="container">
            <div class="breadcrumb">
                <a href="/" class="breadcrumb-item">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-current">Landing Page</span>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Transform Your Lyrics with 
                    <span class="gradient-text">AI-Powered Analysis</span>
                </h1>
                <p class="hero-subtitle">
                    Discover the hidden potential in your songwriting. Get instant feedback, style analysis, 
                    and professional insights powered by advanced AI technology.
                </p>
                <div class="hero-buttons">
                    {% if current_user.is_authenticated %}
                        <a href="/" class="btn btn-primary btn-large dashboard-main-btn">
                            <i class="fas fa-tachometer-alt"></i> Back to Dashboard
                        </a>
                        <a href="/ai/test" class="btn btn-secondary btn-large">
                            <i class="fas fa-flask"></i> AI Test Lab
                        </a>
                        <button onclick="logout()" class="btn btn-outline btn-large logout-btn">
                            <i class="fas fa-sign-out-alt"></i> Log Out
                        </button>
                    {% else %}
                        <button onclick="openSignUpModal()" class="btn btn-primary btn-large">
                            <i class="fas fa-rocket"></i> Start Analyzing Free
                        </button>
                        <button onclick="scrollToDemo()" class="btn btn-secondary btn-large">
                            <i class="fas fa-play"></i> Watch Demo
                        </button>
                    {% endif %}
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">10K+</span>
                        <span class="stat-label">Songs Analyzed</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Artists Detected</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">98%</span>
                        <span class="stat-label">Accuracy Rate</span>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="demo-card">
                    <div class="demo-header">
                        <div class="demo-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="demo-title">LyricLens+ Analysis</span>
                    </div>
                    <div class="demo-content">
                        <div class="demo-line">🎤 Artist Style: <span class="highlight">Taylor Swift (85%)</span></div>
                        <div class="demo-line">📊 Sentiment: <span class="highlight">Positive</span></div>
                        <div class="demo-line">🎵 Genre: <span class="highlight">Pop/Folk</span></div>
                        <div class="demo-line">💰 Commercial Value: <span class="highlight">$4.50</span></div>
                        <div class="demo-line">📖 Storytelling: <span class="highlight">Excellent</span></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Powerful AI Features</h2>
                <p>Everything you need to perfect your songwriting</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <h3>AI Artist Detection</h3>
                    <p>Discover which famous artists your style resembles with 98% accuracy using advanced AI analysis.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Sentiment Analysis</h3>
                    <p>Understand the emotional impact of your lyrics with detailed sentiment and subjectivity scoring.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎵</div>
                    <h3>Rhyme & Flow Analysis</h3>
                    <p>Get insights into your rhyme schemes, patterns, and suggestions for better flow.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📖</div>
                    <h3>Storytelling Metrics</h3>
                    <p>Analyze narrative strength using our 5-element framework: location, actions, thoughts, emotions, dialogue.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>Commercial Value</h3>
                    <p>Estimate the commercial potential of your lyrics with our proprietary ESVAF scoring system.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3>Style Enhancement</h3>
                    <p>Get AI-powered suggestions to improve your lyrics and match specific artist styles.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <div class="section-header">
                <h2>Choose Your Plan</h2>
                <p>Start free, upgrade when you're ready</p>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Free</h3>
                        <div class="price">$0<span>/month</span></div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> 5 analyses per month</li>
                        <li><i class="fas fa-check"></i> Basic sentiment analysis</li>
                        <li><i class="fas fa-check"></i> Rhyme pattern detection</li>
                        <li><i class="fas fa-check"></i> Export to PDF</li>
                    </ul>
                    {% if current_user.is_authenticated %}
                        <a href="/" class="btn btn-outline btn-full">
                            <i class="fas fa-tachometer-alt"></i> Back to Dashboard
                        </a>
                    {% else %}
                        <button onclick="openSignUpModal()" class="btn btn-outline btn-full">Get Started</button>
                    {% endif %}
                </div>
                <div class="pricing-card featured">
                    <div class="pricing-badge">Most Popular</div>
                    <div class="pricing-header">
                        <h3>Pro</h3>
                        <div class="price">$19<span>/month</span></div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited analyses</li>
                        <li><i class="fas fa-check"></i> AI artist detection</li>
                        <li><i class="fas fa-check"></i> Advanced storytelling metrics</li>
                        <li><i class="fas fa-check"></i> Commercial value estimation</li>
                        <li><i class="fas fa-check"></i> All export formats</li>
                        <li><i class="fas fa-check"></i> Priority support</li>
                    </ul>
                    {% if current_user.is_authenticated %}
                        <a href="/ai/test" class="btn btn-primary btn-full">Access Pro Features</a>
                    {% else %}
                        <button onclick="openSignUpModal()" class="btn btn-primary btn-full">Start Pro Trial</button>
                    {% endif %}
                </div>
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Enterprise</h3>
                        <div class="price">$99<span>/month</span></div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Everything in Pro</li>
                        <li><i class="fas fa-check"></i> API access</li>
                        <li><i class="fas fa-check"></i> Custom integrations</li>
                        <li><i class="fas fa-check"></i> Team collaboration</li>
                        <li><i class="fas fa-check"></i> White-label options</li>
                        <li><i class="fas fa-check"></i> Dedicated support</li>
                    </ul>
                    {% if current_user.is_authenticated %}
                        <a href="/api/docs" class="btn btn-outline btn-full">View API Docs</a>
                    {% else %}
                        <button onclick="openSignUpModal()" class="btn btn-outline btn-full">Contact Sales</button>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>

    <!-- Sign Up Modal -->
    <div id="signUpModal" class="modal">
        <div class="modal-content auth-modal">
            <div class="modal-header">
                <h3>Create Your Account</h3>
                <span class="close" onclick="closeSignUpModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="signUpForm" class="auth-form">
                    <div class="form-group">
                        <label for="signUpEmail">Email Address</label>
                        <input type="email" id="signUpEmail" name="email" required placeholder="Enter your email">
                    </div>
                    <div class="form-group">
                        <label for="signUpUsername">Username</label>
                        <input type="text" id="signUpUsername" name="username" required placeholder="Choose a username">
                    </div>
                    <div class="form-group">
                        <label for="signUpPassword">Password</label>
                        <input type="password" id="signUpPassword" name="password" required placeholder="Create a password">
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">Create Account</button>
                </form>
                
                <div class="auth-divider">
                    <span>or continue with</span>
                </div>
                
                <div class="social-auth">
                    <button onclick="signInWithGoogle()" class="btn btn-social btn-google">
                        <i class="fab fa-google"></i> Google
                    </button>
                    <button onclick="signInWithFacebook()" class="btn btn-social btn-facebook">
                        <i class="fab fa-facebook-f"></i> Facebook
                    </button>
                    <button onclick="signInWithGithub()" class="btn btn-social btn-github">
                        <i class="fab fa-github"></i> GitHub
                    </button>
                </div>
                
                <div class="auth-footer">
                    Already have an account? <a href="#" onclick="switchToSignIn()">Sign In</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Sign In Modal -->
    <div id="signInModal" class="modal">
        <div class="modal-content auth-modal">
            <div class="modal-header">
                <h3>Welcome Back</h3>
                <span class="close" onclick="closeSignInModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="signInForm" class="auth-form">
                    <div class="form-group">
                        <label for="signInUsername">Username or Email</label>
                        <input type="text" id="signInUsername" name="username" required placeholder="Enter username or email">
                    </div>
                    <div class="form-group">
                        <label for="signInPassword">Password</label>
                        <input type="password" id="signInPassword" name="password" required placeholder="Enter your password">
                    </div>
                    <div class="form-options">
                        <label class="checkbox">
                            <input type="checkbox" name="remember"> Remember me
                        </label>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">Sign In</button>
                </form>
                
                <div class="auth-divider">
                    <span>or continue with</span>
                </div>
                
                <div class="social-auth">
                    <button onclick="signInWithGoogle()" class="btn btn-social btn-google">
                        <i class="fab fa-google"></i> Google
                    </button>
                    <button onclick="signInWithFacebook()" class="btn btn-social btn-facebook">
                        <i class="fab fa-facebook-f"></i> Facebook
                    </button>
                    <button onclick="signInWithGithub()" class="btn btn-social btn-github">
                        <i class="fab fa-github"></i> GitHub
                    </button>
                </div>
                
                <div class="auth-footer">
                    Don't have an account? <a href="#" onclick="switchToSignUp()">Sign Up</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>🎵 LyricLens+</h3>
                    <p>AI-powered lyric analysis for the next generation of songwriters.</p>
                </div>
                <div class="footer-section">
                    <h4>Product</h4>
                    <a href="#features">Features</a>
                    <a href="#pricing">Pricing</a>
                    <a href="/api/docs">API Docs</a>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <a href="#about">About</a>
                    <a href="#">Contact</a>
                    <a href="#">Privacy</a>
                </div>
                <div class="footer-section">
                    <h4>Connect</h4>
                    <a href="#"><i class="fab fa-twitter"></i> Twitter</a>
                    <a href="#"><i class="fab fa-linkedin"></i> LinkedIn</a>
                    <a href="#"><i class="fab fa-github"></i> GitHub</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 LyricLens+. Created by A A Mupemhi for Benjamin Music. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Floating Dashboard Button (for logged-in users) -->
    {% if current_user.is_authenticated %}
    <div id="floatingDashboard" class="floating-dashboard">
        <a href="/" class="floating-dashboard-btn">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
    </div>
    {% endif %}

    <!-- Debug Info (remove in production) -->
    <div id="debugInfo" style="position: fixed; bottom: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
        <strong>Debug Info:</strong><br>
        Authenticated: {{ current_user.is_authenticated }}<br>
        {% if current_user.is_authenticated %}
            User: {{ current_user.username }}<br>
            ID: {{ current_user.id }}
        {% else %}
            User: Not logged in
        {% endif %}
        <br>
        <button onclick="quickAdminLogin(1)" style="background: #28a745; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Login Admin1</button>
        <button onclick="quickAdminLogin(2)" style="background: #17a2b8; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Login Admin2</button>
        <button onclick="this.parentElement.style.display='none'" style="background: #dc3545; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer;">Hide</button>
    </div>

    <script src="{{ url_for('static', filename='landing.js') }}"></script>
</body>
</html>
