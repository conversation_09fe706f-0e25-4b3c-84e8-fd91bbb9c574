<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple API Test - LyricLens+</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #6e48aa, #9d50bb);
            color: white;
        }
        .container {
            background: white;
            color: black;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        button {
            background: linear-gradient(90deg, #6f42c1, #8e44ad);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: 600;
        }
        button:hover {
            background: linear-gradient(90deg, #5a32a3, #7d3c98);
        }
        .result {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple API Test</h1>
        <p>Test the LyricLens+ API endpoints with your DeepSeek integration</p>

        <div class="test-section">
            <h2>📝 Test Lyrics</h2>
            <textarea id="lyrics" placeholder="Enter your lyrics here...">I am walking down this empty street tonight
Thinking about you and the way you used to smile
My heart is racing with every step I take
Wondering if you're thinking of me too</textarea>
        </div>

        <div class="test-section">
            <h2>🔍 API Health Check</h2>
            <button onclick="testHealth()">Check API Health</button>
            <div id="healthResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🎤 Artist Detection Test</h2>
            <button onclick="testArtistDetection()">Detect Artist Style</button>
            <div id="artistResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>✨ Enhancement Test</h2>
            <button onclick="testEnhancement()">Get Enhancement Suggestions</button>
            <div id="enhanceResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🎵 Rhyme Generation Test</h2>
            <button onclick="testRhymes()">Generate Rhymes for "night"</button>
            <div id="rhymeResult" class="result" style="display: none;"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/" style="color: #6f42c1; text-decoration: none; font-weight: bold;">← Back to LyricLens+</a>
        </div>
    </div>

    <script>
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = isError ? 'result error' : 'result success';
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result';
            element.textContent = 'Loading... 🤖';
        }

        async function testHealth() {
            showLoading('healthResult');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                showResult('healthResult', data, !response.ok);
            } catch (error) {
                showResult('healthResult', `Error: ${error.message}`, true);
            }
        }

        async function testArtistDetection() {
            showLoading('artistResult');
            const lyrics = document.getElementById('lyrics').value;
            
            try {
                const response = await fetch('/api/detect-artist', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'lyriclens-secret-key-2025'
                    },
                    body: JSON.stringify({
                        text: lyrics,
                        ai_provider: 'deepseek'
                    })
                });
                
                const data = await response.json();
                showResult('artistResult', data, !response.ok);
            } catch (error) {
                showResult('artistResult', `Error: ${error.message}`, true);
            }
        }

        async function testEnhancement() {
            showLoading('enhanceResult');
            const lyrics = document.getElementById('lyrics').value;
            
            try {
                const response = await fetch('/api/enhance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'lyriclens-secret-key-2025'
                    },
                    body: JSON.stringify({
                        text: lyrics,
                        ai_provider: 'deepseek',
                        focus_area: 'general'
                    })
                });
                
                const data = await response.json();
                showResult('enhanceResult', data, !response.ok);
            } catch (error) {
                showResult('enhanceResult', `Error: ${error.message}`, true);
            }
        }

        async function testRhymes() {
            showLoading('rhymeResult');
            
            try {
                const response = await fetch('/api/generate-rhymes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'lyriclens-secret-key-2025'
                    },
                    body: JSON.stringify({
                        word: 'night',
                        context: 'romantic ballad',
                        ai_provider: 'deepseek'
                    })
                });
                
                const data = await response.json();
                showResult('rhymeResult', data, !response.ok);
            } catch (error) {
                showResult('rhymeResult', `Error: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
