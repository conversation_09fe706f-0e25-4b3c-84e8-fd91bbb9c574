<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Features Test - LyricLens+</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-button {
            background: linear-gradient(90deg, #6f42c1, #8e44ad);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: 600;
        }
        .test-button:hover {
            background: linear-gradient(90deg, #5a32a3, #7d3c98);
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .loading {
            color: #6f42c1;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header" style="text-align: center; margin-bottom: 40px; padding: 40px 0; background: linear-gradient(135deg, #6e48aa, #9d50bb); color: white; border-radius: 12px;">
            <h1>🤖 AI Features Test Lab</h1>
            <p>Test all AI-powered features with your DeepSeek API</p>
        </div>

        <!-- Sample Lyrics Input -->
        <div class="test-section">
            <h2>📝 Test Lyrics</h2>
            <textarea id="testLyrics" rows="4" style="width: 100%; padding: 10px; border-radius: 6px; border: 1px solid #ddd;">I am walking down this empty street tonight
Thinking about you and the way you used to smile
My heart is racing with every step I take
Wondering if you're thinking of me too</textarea>
        </div>

        <!-- AI Analysis Test -->
        <div class="test-section">
            <h2>🎯 1. AI-Enhanced Analysis</h2>
            <p>Get comprehensive AI analysis of your lyrics</p>
            <button class="test-button" onclick="testAIAnalysis()">Test AI Analysis</button>
            <div id="aiAnalysisResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Enhancement Suggestions -->
        <div class="test-section">
            <h2>✨ 2. Enhancement Suggestions</h2>
            <p>Get AI-powered suggestions for improvement</p>
            <button class="test-button" onclick="testEnhancement('general')">General Enhancement</button>
            <button class="test-button" onclick="testEnhancement('story')">Story Enhancement</button>
            <button class="test-button" onclick="testEnhancement('rhyme')">Rhyme Enhancement</button>
            <button class="test-button" onclick="testEnhancement('emotion')">Emotion Enhancement</button>
            <div id="enhancementResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Rhyme Generation -->
        <div class="test-section">
            <h2>🎵 3. AI Rhyme Generation</h2>
            <p>Generate creative rhymes for any word</p>
            <input type="text" id="rhymeWord" placeholder="Enter a word (e.g., 'night')" style="padding: 8px; margin-right: 10px; border-radius: 4px; border: 1px solid #ddd;">
            <button class="test-button" onclick="testRhymeGeneration()">Generate Rhymes</button>
            <div id="rhymeResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Style Comparison -->
        <div class="test-section">
            <h2>🎤 4. Artist Style Comparison</h2>
            <p>Compare your lyrics to famous artists</p>
            <input type="text" id="artistName" placeholder="Enter artist name (e.g., 'Taylor Swift')" style="padding: 8px; margin-right: 10px; border-radius: 4px; border: 1px solid #ddd;">
            <button class="test-button" onclick="testStyleComparison()">Compare Style</button>
            <button class="test-button" onclick="testStyleComparison('')">Find Similar Artists</button>
            <div id="styleResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Artist Detection -->
        <div class="test-section">
            <h2>🎤 5. AI Artist Detection</h2>
            <p>Discover which artists your lyrics sound like</p>
            <button class="test-button" onclick="testArtistDetection()">Detect Artist Style</button>
            <div id="artistDetectionResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Storytelling Feedback -->
        <div class="test-section">
            <h2>📖 6. Storytelling Feedback</h2>
            <p>Get detailed storytelling analysis and suggestions</p>
            <button class="test-button" onclick="testStorytellingFeedback()">Get Story Feedback</button>
            <div id="storyResult" class="result-box" style="display: none;"></div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="/" class="test-button">← Back to LyricLens+</a>
            <a href="/api/docs" class="test-button">View API Docs</a>
        </div>
    </div>

    <script>
        const API_KEY = 'lyriclens-secret-key-2025';
        const BASE_URL = window.location.origin + '/api';

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result-box loading';
            element.textContent = 'Loading... AI is thinking 🤖';
        }

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = isError ? 'result-box error' : 'result-box success';
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function makeAPICall(endpoint, data) {
            try {
                console.log(`Making API call to: ${BASE_URL}${endpoint}`);
                const response = await fetch(`${BASE_URL}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': API_KEY
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('API Response:', result);
                return result;
            } catch (error) {
                console.error('API Error:', error);
                return {
                    error: `API call failed: ${error.message}`,
                    details: error.toString()
                };
            }
        }

        async function testAIAnalysis() {
            showLoading('aiAnalysisResult');
            const lyrics = document.getElementById('testLyrics').value;
            const result = await makeAPICall('/analyze', {
                text: lyrics,
                ai_enhanced: true,
                ai_provider: 'deepseek'
            });
            showResult('aiAnalysisResult', result, !!result.error);
        }

        async function testEnhancement(focusArea) {
            showLoading('enhancementResult');
            const lyrics = document.getElementById('testLyrics').value;
            const result = await makeAPICall('/enhance', {
                text: lyrics,
                ai_provider: 'deepseek',
                focus_area: focusArea
            });
            showResult('enhancementResult', result, !!result.error);
        }

        async function testRhymeGeneration() {
            showLoading('rhymeResult');
            const word = document.getElementById('rhymeWord').value || 'night';
            const lyrics = document.getElementById('testLyrics').value;
            const result = await makeAPICall('/generate-rhymes', {
                word: word,
                context: lyrics,
                ai_provider: 'deepseek'
            });
            showResult('rhymeResult', result, !!result.error);
        }

        async function testStyleComparison(artist = null) {
            showLoading('styleResult');
            const lyrics = document.getElementById('testLyrics').value;
            const artistName = artist || document.getElementById('artistName').value;
            const result = await makeAPICall('/compare-style', {
                text: lyrics,
                artist: artistName,
                ai_provider: 'deepseek'
            });
            showResult('styleResult', result, !!result.error);
        }

        async function testArtistDetection() {
            showLoading('artistDetectionResult');
            const lyrics = document.getElementById('testLyrics').value;
            const result = await makeAPICall('/detect-artist', {
                text: lyrics,
                ai_provider: 'deepseek',
                confidence_threshold: 0.7
            });
            showResult('artistDetectionResult', result, !!result.error);
        }

        async function testStorytellingFeedback() {
            showLoading('storyResult');
            const lyrics = document.getElementById('testLyrics').value;
            const result = await makeAPICall('/story-feedback', {
                text: lyrics,
                ai_provider: 'deepseek'
            });
            showResult('storyResult', result, !!result.error);
        }
    </script>
</body>
</html>
