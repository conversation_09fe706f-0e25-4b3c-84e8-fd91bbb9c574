{% extends "base.html" %}

{% block title %}Share Analysis - LyricLens+{% endblock %}

{% block content %}
<div class="share-container">
    <h2>Share "{{ analysis.title }}"</h2>
    
    <div class="analysis-preview">
        <h3>Analysis Preview</h3>
        <p class="preview-text">{{ analysis.text[:200] }}{% if analysis.text|length > 200 %}...{% endif %}</p>
        <p class="preview-date">Created: {{ analysis.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
        <div class="preview-badges">
            {% if analysis.public %}
                <span class="badge public">Public</span>
                <p class="public-note">This analysis is public and can be viewed by anyone with the link.</p>
            {% else %}
                <span class="badge private">Private</span>
                <p class="private-note">This analysis is private and can only be viewed by you and people you share it with.</p>
            {% endif %}
        </div>
        
        <form method="POST" action="{{ url_for('toggle_public', analysis_id=analysis.id) }}" class="public-toggle-form">
            <button type="submit" class="btn toggle-btn">
                {% if analysis.public %}Make Private{% else %}Make Public{% endif %}
            </button>
        </form>
    </div>
    
    <div class="share-form-container">
        <h3>Share with a User</h3>
        <form method="POST" class="share-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required placeholder="Enter username to share with">
            </div>
            <div class="form-group checkbox">
                <input type="checkbox" id="can_edit" name="can_edit">
                <label for="can_edit">Allow editing</label>
            </div>
            <button type="submit" class="btn share-btn">Share</button>
        </form>
    </div>
    
    <div class="current-shares">
        <h3>Currently Shared With</h3>
        {% if shares %}
            <div class="shares-list">
                {% for share in shares %}
                    <div class="share-item">
                        <div class="share-info">
                            <span class="share-username">{{ share.shared_with.username }}</span>
                            <span class="share-date">Shared on: {{ share.created_at.strftime('%Y-%m-%d') }}</span>
                            <span class="share-permission {% if share.can_edit %}can-edit{% else %}view-only{% endif %}">
                                {% if share.can_edit %}Can Edit{% else %}View Only{% endif %}
                            </span>
                        </div>
                        <div class="share-actions">
                            <form method="POST" action="{{ url_for('remove_share', share_id=share.id) }}">
                                <button type="submit" class="btn remove-btn">Remove</button>
                            </form>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="empty-state">You haven't shared this analysis with anyone yet.</p>
        {% endif %}
    </div>
    
    <div class="share-link">
        <h3>Share Link</h3>
        <div class="link-container">
            <input type="text" id="share-link" value="{{ url_for('view_analysis', analysis_id=analysis.id, _external=True) }}" readonly>
            <button onclick="copyShareLink()" class="btn copy-btn">Copy</button>
        </div>
        <p class="link-note">Anyone with this link can view this analysis {% if not analysis.public %}if they have permission{% endif %}.</p>
    </div>
    
    <div class="back-link">
        <a href="{{ url_for('dashboard') }}" class="btn back-btn">Back to Dashboard</a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function copyShareLink() {
        const shareLink = document.getElementById('share-link');
        shareLink.select();
        document.execCommand('copy');
        
        const copyBtn = document.querySelector('.copy-btn');
        const originalText = copyBtn.textContent;
        copyBtn.textContent = 'Copied!';
        
        setTimeout(() => {
            copyBtn.textContent = originalText;
        }, 2000);
    }
</script>
{% endblock %}