<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LyricLens+ API Documentation</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .api-docs {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .endpoint {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            font-size: 12px;
        }
        .method.post { background: #28a745; }
        .method.get { background: #007bff; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, #6e48aa, #9d50bb);
            color: white;
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <div class="api-docs">
        <div class="header">
            <h1>🎵 LyricLens+ API Documentation</h1>
            <p>Powerful AI-enhanced lyric analysis API with Grok and DeepSeek integration</p>
        </div>

        <div class="endpoint">
            <h2>🔑 Authentication</h2>
            <p>All API endpoints require authentication using an API key. Include your API key in the request headers:</p>
            <div class="code-block">
                <strong>Header:</strong> X-API-Key: your_api_key_here<br>
                <strong>Or URL Parameter:</strong> ?api_key=your_api_key_here
            </div>
        </div>

        <div class="endpoint">
            <h2><span class="method get">GET</span> /api/health</h2>
            <p>Check API health and available features</p>
            <div class="code-block">
<strong>Response:</strong>
{
  "status": "healthy",
  "version": "1.0.0",
  "features": {
    "grok_available": true,
    "deepseek_available": true
  }
}
            </div>
        </div>

        <div class="endpoint">
            <h2><span class="method post">POST</span> /api/analyze</h2>
            <p>Analyze lyrics with optional AI enhancement</p>
            <div class="code-block">
<strong>Request Body:</strong>
{
  "text": "Your lyrics here...",
  "ai_enhanced": true,
  "ai_provider": "grok"
}
            </div>
            <div class="code-block">
<strong>Response:</strong>
{
  "status": "success",
  "data": {
    "External_Details": {...},
    "Internal_Details": {...},
    "Sentiment_Analysis": {...},
    "Storytelling_Analysis": {...},
    "AI_Analysis": {...}
  }
}
            </div>
        </div>

        <div class="endpoint">
            <h2><span class="method post">POST</span> /api/enhance</h2>
            <p>Get AI-powered suggestions for lyric improvement</p>
            <div class="code-block">
<strong>Request Body:</strong>
{
  "text": "Your lyrics here...",
  "ai_provider": "grok",
  "focus_area": "general"
}
            </div>
            <p><strong>Focus Areas:</strong> general, rhyme, story, emotion</p>
        </div>

        <div class="endpoint">
            <h2><span class="method post">POST</span> /api/compare-style</h2>
            <p>Compare lyrics to famous artists or identify similar styles</p>
            <div class="code-block">
<strong>Request Body:</strong>
{
  "text": "Your lyrics here...",
  "artist": "Taylor Swift",
  "ai_provider": "deepseek"
}
            </div>
        </div>

        <div class="endpoint">
            <h2><span class="method post">POST</span> /api/generate-rhymes</h2>
            <p>Generate creative rhymes for a given word</p>
            <div class="code-block">
<strong>Request Body:</strong>
{
  "word": "love",
  "context": "romantic ballad",
  "ai_provider": "grok"
}
            </div>
        </div>

        <div class="endpoint">
            <h2><span class="method post">POST</span> /api/detect-artist</h2>
            <p>AI-powered artist detection - identify which artists your lyrics sound like</p>
            <div class="code-block">
<strong>Request Body:</strong>
{
  "text": "Your lyrics here...",
  "ai_provider": "deepseek",
  "confidence_threshold": 0.7
}
            </div>
            <div class="code-block">
<strong>Response:</strong>
{
  "status": "success",
  "artist_detection": {
    "primary_match": {
      "artist": "Taylor Swift",
      "confidence": 0.85,
      "reasoning": "Strong narrative storytelling..."
    },
    "secondary_matches": [...],
    "style_characteristics": [...],
    "genre_indicators": ["Pop", "Folk"]
  }
}
            </div>
        </div>

        <div class="endpoint">
            <h2><span class="method post">POST</span> /api/story-feedback</h2>
            <p>Get detailed storytelling analysis and improvement suggestions</p>
            <div class="code-block">
<strong>Request Body:</strong>
{
  "text": "Your lyrics here...",
  "ai_provider": "grok"
}
            </div>
        </div>

        <div class="endpoint">
            <h2>🚀 Example Usage</h2>
            <div class="code-block">
<strong>cURL Example:</strong>
curl -X POST "http://localhost:5000/api/analyze" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "text": "I'\''m walking down this empty street tonight...",
    "ai_enhanced": true,
    "ai_provider": "grok"
  }'
            </div>
        </div>

        <div class="endpoint">
            <h2>🔧 AI Providers</h2>
            <p><strong>Grok:</strong> X.AI's advanced language model - excellent for creative analysis</p>
            <p><strong>DeepSeek:</strong> Powerful reasoning model - great for technical analysis</p>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="/" class="btn" style="background: linear-gradient(90deg, #6e48aa, #9d50bb); color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px;">← Back to LyricLens+</a>
        </div>
    </div>
</body>
</html>
