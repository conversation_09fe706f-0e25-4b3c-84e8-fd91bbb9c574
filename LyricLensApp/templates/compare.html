<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>LyricLens+ Comparison Results</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>LyricLens+ with ESVAF Analyzer</h1>
            <p>Created by <PERSON><PERSON><PERSON></p>
        </header>

        <div class="mode-selector">
            <a href="/" class="mode-btn">Back to Single Analysis</a>
        </div>

        <div class="comparison-results">
            <h2>Lyrics Comparison Results</h2>

            <div class="comparison-grid">
                <div class="comparison-header"></div>
                <div class="comparison-header">Lyrics A</div>
                <div class="comparison-header">Lyrics B</div>
                
                <!-- Input Text -->
                <div class="comparison-row-header">Input Text</div>
                <div class="comparison-cell">
                    <pre>{{ analysis_a.input_text }}</pre>
                </div>
                <div class="comparison-cell">
                    <pre>{{ analysis_b.input_text }}</pre>
                </div>
                
                <!-- Sentiment Analysis -->
                <div class="comparison-row-header">Sentiment</div>
                <div class="comparison-cell">
                    <p><strong>{{ analysis_a.Sentiment_Analysis.sentiment }}</strong></p>
                    <p>Polarity: {{ analysis_a.Sentiment_Analysis.polarity|round(2) }}</p>
                    <p>Subjectivity: {{ analysis_a.Sentiment_Analysis.subjectivity|round(2) }}</p>
                </div>
                <div class="comparison-cell">
                    <p><strong>{{ analysis_b.Sentiment_Analysis.sentiment }}</strong></p>
                    <p>Polarity: {{ analysis_b.Sentiment_Analysis.polarity|round(2) }}</p>
                    <p>Subjectivity: {{ analysis_b.Sentiment_Analysis.subjectivity|round(2) }}</p>
                </div>
                
                <!-- Rhyme Analysis -->
                <div class="comparison-row-header">Rhyme Density</div>
                <div class="comparison-cell">
                    {{ (analysis_a.Rhyme_Analysis.rhyme_density * 100)|round(1) }}%
                </div>
                <div class="comparison-cell">
                    {{ (analysis_b.Rhyme_Analysis.rhyme_density * 100)|round(1) }}%
                </div>
                
                <!-- Top Genre -->
                <div class="comparison-row-header">Top Genre</div>
                <div class="comparison-cell">
                    {% if analysis_a.Genre_Classification.top_genres %}
                        {{ analysis_a.Genre_Classification.top_genres[0][0] }} ({{ analysis_a.Genre_Classification.top_genres[0][1]|round(1) }}%)
                    {% else %}
                        No genre detected
                    {% endif %}
                </div>
                <div class="comparison-cell">
                    {% if analysis_b.Genre_Classification.top_genres %}
                        {{ analysis_b.Genre_Classification.top_genres[0][0] }} ({{ analysis_b.Genre_Classification.top_genres[0][1]|round(1) }}%)
                    {% else %}
                        No genre detected
                    {% endif %}
                </div>
                
                <!-- External Details -->
                <div class="comparison-row-header">External Details</div>
                <div class="comparison-cell">
                    <p><strong>Locations:</strong> {{ analysis_a.External_Details.Location|length }}</p>
                    <p><strong>Actions:</strong> {{ analysis_a.External_Details.Actions|length }}</p>
                    <p><strong>Nouns:</strong> {{ analysis_a.External_Details.Nouns|length }}</p>
                    <p><strong>Verbs:</strong> {{ analysis_a.External_Details.Verbs|length }}</p>
                </div>
                <div class="comparison-cell">
                    <p><strong>Locations:</strong> {{ analysis_b.External_Details.Location|length }}</p>
                    <p><strong>Actions:</strong> {{ analysis_b.External_Details.Actions|length }}</p>
                    <p><strong>Nouns:</strong> {{ analysis_b.External_Details.Nouns|length }}</p>
                    <p><strong>Verbs:</strong> {{ analysis_b.External_Details.Verbs|length }}</p>
                </div>
                
                <!-- Internal Details -->
                <div class="comparison-row-header">Internal Details</div>
                <div class="comparison-cell">
                    <p><strong>Thoughts:</strong> {{ analysis_a.Internal_Details.Thoughts|length }}</p>
                    <p><strong>Emotions:</strong> {{ analysis_a.Internal_Details.Emotions|length }}</p>
                    <p><strong>Dialogue:</strong> {{ analysis_a.Internal_Details.Dialogue|length }}</p>
                </div>
                <div class="comparison-cell">
                    <p><strong>Thoughts:</strong> {{ analysis_b.Internal_Details.Thoughts|length }}</p>
                    <p><strong>Emotions:</strong> {{ analysis_b.Internal_Details.Emotions|length }}</p>
                    <p><strong>Dialogue:</strong> {{ analysis_b.Internal_Details.Dialogue|length }}</p>
                </div>
                
                <!-- Total Value -->
                <div class="comparison-row-header">Total Value</div>
                <div class="comparison-cell">
                    {{ analysis_a.Total_Value }} cents (${{ (analysis_a.Total_Value / 100)|round(2) }})
                </div>
                <div class="comparison-cell">
                    {{ analysis_b.Total_Value }} cents (${{ (analysis_b.Total_Value / 100)|round(2) }})
                </div>
                
                <!-- Similar Works -->
                <div class="comparison-row-header">Similar Works</div>
                <div class="comparison-cell">
                    {% if analysis_a.Similar_Works %}
                        <ul class="similar-works-list">
                            {% for work in analysis_a.Similar_Works %}
                                <li>
                                    <strong>{{ work.title }}</strong> ({{ work.year }}) by {{ work.creator }}
                                    <br><small>{{ work.reason }}</small>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>No similar works found</p>
                    {% endif %}
                </div>
                <div class="comparison-cell">
                    {% if analysis_b.Similar_Works %}
                        <ul class="similar-works-list">
                            {% for work in analysis_b.Similar_Works %}
                                <li>
                                    <strong>{{ work.title }}</strong> ({{ work.year }}) by {{ work.creator }}
                                    <br><small>{{ work.reason }}</small>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>No similar works found</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="comparison-insights">
                <h3>Key Insights</h3>
                <ul>
                    {% if analysis_a.Sentiment_Analysis.polarity > analysis_b.Sentiment_Analysis.polarity %}
                        <li>Lyrics A has a more positive tone than Lyrics B.</li>
                    {% elif analysis_b.Sentiment_Analysis.polarity > analysis_a.Sentiment_Analysis.polarity %}
                        <li>Lyrics B has a more positive tone than Lyrics A.</li>
                    {% else %}
                        <li>Both lyrics have similar emotional tones.</li>
                    {% endif %}
                    
                    {% if analysis_a.Rhyme_Analysis.rhyme_density > analysis_b.Rhyme_Analysis.rhyme_density %}
                        <li>Lyrics A has a higher rhyme density than Lyrics B.</li>
                    {% elif analysis_b.Rhyme_Analysis.rhyme_density > analysis_a.Rhyme_Analysis.rhyme_density %}
                        <li>Lyrics B has a higher rhyme density than Lyrics A.</li>
                    {% else %}
                        <li>Both lyrics have similar rhyme patterns.</li>
                    {% endif %}
                    
                    {% if analysis_a.Total_Value > analysis_b.Total_Value %}
                        <li>Lyrics A has a higher commercial value than Lyrics B.</li>
                    {% elif analysis_b.Total_Value > analysis_a.Total_Value %}
                        <li>Lyrics B has a higher commercial value than Lyrics A.</li>
                    {% else %}
                        <li>Both lyrics have similar commercial value.</li>
                    {% endif %}
                    
                    {% if analysis_a.External_Details.Actions|length > analysis_b.External_Details.Actions|length %}
                        <li>Lyrics A contains more action descriptions than Lyrics B.</li>
                    {% elif analysis_b.External_Details.Actions|length > analysis_a.External_Details.Actions|length %}
                        <li>Lyrics B contains more action descriptions than Lyrics A.</li>
                    {% endif %}
                    
                    {% if analysis_a.Internal_Details.Emotions|length > analysis_b.Internal_Details.Emotions|length %}
                        <li>Lyrics A expresses more emotions than Lyrics B.</li>
                    {% elif analysis_b.Internal_Details.Emotions|length > analysis_a.Internal_Details.Emotions|length %}
                        <li>Lyrics B expresses more emotions than Lyrics A.</li>
                    {% endif %}
                </ul>
            </div>

            <!-- Export Section -->
            <div class="export-section">
                <h3>Export Your Comparison</h3>
                <p>Save your comparison results in your preferred format for future reference or sharing.</p>
                <div class="export-buttons">
                    <button onclick="printResults()" class="print-btn">Print Comparison</button>
                    <button onclick="openExportModal()" class="export-btn main-export-btn">Export Comparison</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Export Comparison</h3>
                <span class="close" onclick="closeExportModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="export-option-group">
                    <label for="filename">Export As:</label>
                    <input type="text" id="filename" value="lyriclens_comparison" placeholder="Enter filename">
                </div>

                <div class="export-option-group">
                    <label>Format:</label>
                    <div class="format-options">
                        <div class="format-option" onclick="selectFormat('pdf')">
                            <div class="format-icon">📄</div>
                            <div class="format-name">PDF</div>
                            <div class="format-desc">Portable Document Format</div>
                        </div>
                        <div class="format-option" onclick="selectFormat('csv')">
                            <div class="format-icon">📊</div>
                            <div class="format-name">CSV</div>
                            <div class="format-desc">Comma Separated Values</div>
                        </div>
                        <div class="format-option" onclick="selectFormat('txt')">
                            <div class="format-icon">📝</div>
                            <div class="format-name">Text</div>
                            <div class="format-desc">Plain Text File</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="closeExportModal()" class="btn cancel-btn">Cancel</button>
                <button onclick="confirmExport()" class="btn export-confirm-btn">Export</button>
            </div>
        </div>
    </div>

    <script>
        function printResults() {
            const results = document.querySelector('.comparison-results');
            const printWindow = window.open('', '', 'width=800,height=600');
            printWindow.document.write('<html><head><title>LyricLens+ Comparison</title>');
            printWindow.document.write('<style>body{font-family:Arial,sans-serif;}h2,h3,h4{color:#333;}ul{list-style-type:disc;padding-left:20px;}li{margin:5px 0;}.comparison-grid{display:grid;grid-template-columns:1fr 2fr 2fr;gap:10px;}.comparison-header,.comparison-row-header{font-weight:bold;background:#f0f0f0;padding:10px;}.comparison-cell{padding:10px;border:1px solid #ddd;}</style>');
            printWindow.document.write('</head><body>');
            printWindow.document.write(results.innerHTML);
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
        }
        
        function exportComparison(format) {
            // Create a form to submit the comparison data
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/export_comparison/' + format;

            // Add the analysis data as hidden fields
            const analysisDataA = document.createElement('input');
            analysisDataA.type = 'hidden';
            analysisDataA.name = 'analysis_data_a';
            analysisDataA.value = JSON.stringify({{ analysis_a|tojson }});
            form.appendChild(analysisDataA);

            const analysisDataB = document.createElement('input');
            analysisDataB.type = 'hidden';
            analysisDataB.name = 'analysis_data_b';
            analysisDataB.value = JSON.stringify({{ analysis_b|tojson }});
            form.appendChild(analysisDataB);

            // Submit the form
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        // Export Modal Functions
        let selectedFormat = 'pdf';

        function openExportModal() {
            document.getElementById('exportModal').style.display = 'block';
            // Select PDF by default
            selectFormat('pdf');
        }

        function closeExportModal() {
            document.getElementById('exportModal').style.display = 'none';
        }

        function selectFormat(format) {
            selectedFormat = format;
            // Remove active class from all format options
            document.querySelectorAll('.format-option').forEach(option => {
                option.classList.remove('active');
            });
            // Add active class to selected format
            event.target.closest('.format-option').classList.add('active');
        }

        function confirmExport() {
            const filename = document.getElementById('filename').value || 'lyriclens_comparison';
            closeExportModal();
            exportComparison(selectedFormat);
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('exportModal');
            if (event.target == modal) {
                closeExportModal();
            }
        }
    </script>
</body>
</html>
