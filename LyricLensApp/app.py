from flask import Flask, render_template, request, redirect, url_for, flash, session, send_file, Response, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
import os
import json
import uuid
from datetime import datetime
from io import BytesIO, StringIO
import csv
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from PIL import Image
import pytesseract
import PyPDF2
from textblob import TextBlob  # Add this import for sentiment analysis
import re
from collections import defaultdict
import requests
from functools import wraps
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'default-secret-key-for-development')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///lyriclens.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'

# Enable CORS for all routes
CORS(app)

# Define UPLOAD_FOLDER variable
UPLOAD_FOLDER = app.config['UPLOAD_FOLDER']

# AI API Configuration
GROK_API_KEY = os.environ.get('GROK_API_KEY', '')
DEEPSEEK_API_KEY = os.environ.get('DEEPSEEK_API_KEY', '')

# AI API URLs
GROK_API_URL = "https://api.x.ai/v1/chat/completions"
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"

db = SQLAlchemy(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'

# User model
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    analyses = db.relationship('Analysis', backref='owner', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

# Analysis model
class Analysis(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    text = db.Column(db.Text, nullable=False)
    result = db.Column(db.Text, nullable=False)  # JSON string of analysis results
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    public = db.Column(db.Boolean, default=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    shares = db.relationship('Share', backref='analysis', lazy=True, cascade="all, delete-orphan")

# Share model
class Share(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    analysis_id = db.Column(db.Integer, db.ForeignKey('analysis.id'), nullable=False)
    shared_with_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    can_edit = db.Column(db.Boolean, default=False)
    
    shared_with = db.relationship('User', foreign_keys=[shared_with_id])

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# AI Helper Functions
def call_grok_api(prompt, max_tokens=1000):
    """Call Grok API for AI analysis"""
    if not GROK_API_KEY:
        return {"error": "Grok API key not configured"}

    headers = {
        "Authorization": f"Bearer {GROK_API_KEY}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "grok-beta",
        "messages": [
            {"role": "system", "content": "You are an expert lyricist and music analyst. Provide insightful, creative, and constructive feedback on lyrics and songwriting."},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": max_tokens,
        "temperature": 0.7
    }

    try:
        response = requests.post(GROK_API_URL, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"Grok API error: {response.status_code}"}
    except Exception as e:
        return {"error": f"Grok API call failed: {str(e)}"}

def call_deepseek_api(prompt, max_tokens=1000):
    """Call DeepSeek API for AI analysis"""
    if not DEEPSEEK_API_KEY:
        return {"error": "DeepSeek API key not configured"}

    headers = {
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": "You are an expert lyricist and music analyst. Provide insightful, creative, and constructive feedback on lyrics and songwriting."},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": max_tokens,
        "temperature": 0.7
    }

    try:
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"DeepSeek API error: {response.status_code}"}
    except Exception as e:
        return {"error": f"DeepSeek API call failed: {str(e)}"}

# API Authentication decorator
def api_key_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
        if not api_key:
            return jsonify({"error": "API key required"}), 401
        # For now, we'll use a simple API key check
        # In production, you'd want to store API keys in the database
        if api_key != app.config['SECRET_KEY']:
            return jsonify({"error": "Invalid API key"}), 401
        return f(*args, **kwargs)
    return decorated_function

# Add sentiment analysis function
def analyze_sentiment(text):
    analysis = TextBlob(text)
    # Get polarity score (-1 to 1, where -1 is negative, 0 is neutral, 1 is positive)
    polarity = analysis.sentiment.polarity
    # Get subjectivity score (0 to 1, where 0 is objective, 1 is subjective)
    subjectivity = analysis.sentiment.subjectivity
    
    # Determine sentiment category
    if polarity > 0.1:
        sentiment = "Positive"
    elif polarity < -0.1:
        sentiment = "Negative"
    else:
        sentiment = "Neutral"
        
    return {
        "sentiment": sentiment,
        "polarity": polarity,
        "subjectivity": subjectivity
    }

# Add this function for rhyme detection
def detect_rhyme_patterns(text):
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    # Extract last word from each line
    last_words = []
    for line in lines:
        # Remove punctuation from the end of the line
        clean_line = re.sub(r'[^\w\s]$', '', line.strip())
        words = clean_line.split()
        if words:
            last_words.append(words[-1].lower())
        else:
            last_words.append("")
    
    # Group by potential rhymes (using last 2 characters as simple heuristic)
    rhyme_groups = defaultdict(list)
    for i, word in enumerate(last_words):
        if len(word) >= 2:
            rhyme_key = word[-2:]
            rhyme_groups[rhyme_key].append(i)
    
    # Create rhyme scheme (A, B, C, etc.)
    rhyme_scheme = [""] * len(lines)
    current_scheme = 'A'
    
    for rhyme_key, positions in rhyme_groups.items():
        if len(positions) >= 2:  # Only consider it a rhyme if at least 2 lines share it
            for pos in positions:
                rhyme_scheme[pos] = current_scheme
            current_scheme = chr(ord(current_scheme) + 1)
    
    # Format results
    result = []
    for i, (line, scheme) in enumerate(zip(lines, rhyme_scheme)):
        if scheme:
            result.append(f"Line {i+1}: {line} [Rhyme: {scheme}]")
        else:
            result.append(f"Line {i+1}: {line} [No rhyme detected]")
    
    # Calculate rhyme density (percentage of lines that rhyme)
    rhyming_lines = sum(1 for scheme in rhyme_scheme if scheme)
    rhyme_density = rhyming_lines / len(lines) if lines else 0
    
    return {
        "rhyme_scheme": rhyme_scheme,
        "annotated_lines": result,
        "rhyme_density": rhyme_density
    }

# Add storytelling analysis function
def analyze_storytelling(text):
    lines = [line.strip() for line in text.split('\n') if line.strip()]

    result = {
        'Location': [],
        'Actions': [],
        'Thoughts': [],
        'Emotions': [],
        'Dialogue': [],
        'storytelling_score': 0,
        'storytelling_density': 0
    }

    # Location indicators - where the story takes place
    location_keywords = [
        'in the', 'at the', 'on the', 'inside', 'outside', 'upstairs', 'downstairs',
        'room', 'house', 'car', 'street', 'park', 'office', 'school', 'club', 'bar',
        'kitchen', 'bedroom', 'bathroom', 'living room', 'garage', 'basement',
        'city', 'town', 'beach', 'mountain', 'forest', 'highway', 'bridge',
        'corner', 'alley', 'rooftop', 'balcony', 'window', 'door', 'floor'
    ]

    # Action indicators - what's happening in the moment
    action_keywords = [
        'walking', 'running', 'sitting', 'standing', 'lying', 'driving', 'flying',
        'dancing', 'singing', 'crying', 'laughing', 'screaming', 'whispering',
        'looking', 'watching', 'listening', 'touching', 'holding', 'grabbing',
        'opening', 'closing', 'pushing', 'pulling', 'throwing', 'catching',
        'jumping', 'falling', 'climbing', 'crawling', 'swimming', 'fighting'
    ]

    # Thought indicators - internal mental state
    thought_keywords = [
        'i think', 'i thought', 'thinking', 'wondering', 'realizing', 'remembering',
        'imagining', 'dreaming', 'hoping', 'wishing', 'planning', 'deciding',
        'questioning', 'doubting', 'believing', 'knowing', 'understanding',
        'in my mind', 'in my head', 'mentally', 'internally'
    ]

    # Emotion indicators - feelings shown through body language/physical reactions
    emotion_keywords = [
        'heart racing', 'heart pounding', 'sweating', 'shaking', 'trembling',
        'tears', 'crying', 'smiling', 'grinning', 'frowning', 'sighing',
        'breathing heavy', 'holding breath', 'gasping', 'choking',
        'blushing', 'turning red', 'going pale', 'freezing up',
        'jumping up', 'falling down', 'leaning back', 'stepping forward',
        'clenching fists', 'crossing arms', 'covering face', 'hiding'
    ]

    # Dialogue indicators - direct speech
    dialogue_indicators = [
        '"', "'", 'said', 'says', 'told', 'tells', 'asked', 'asks',
        'whispered', 'whispers', 'shouted', 'shouts', 'screamed', 'screams',
        'replied', 'replies', 'responded', 'responds', 'called', 'calls'
    ]

    total_elements = 0

    for line in lines:
        lower_line = line.lower()

        # Check for location elements
        if any(keyword in lower_line for keyword in location_keywords):
            if line not in result['Location']:
                result['Location'].append(line)
                total_elements += 1

        # Check for action elements
        if any(keyword in lower_line for keyword in action_keywords):
            if line not in result['Actions']:
                result['Actions'].append(line)
                total_elements += 1

        # Check for thought elements
        if any(keyword in lower_line for keyword in thought_keywords):
            if line not in result['Thoughts']:
                result['Thoughts'].append(line)
                total_elements += 1

        # Check for emotion elements (physical manifestations)
        if any(keyword in lower_line for keyword in emotion_keywords):
            if line not in result['Emotions']:
                result['Emotions'].append(line)
                total_elements += 1

        # Check for dialogue elements
        if any(indicator in line for indicator in dialogue_indicators):
            if line not in result['Dialogue']:
                result['Dialogue'].append(line)
                total_elements += 1

    # Calculate storytelling metrics
    result['storytelling_score'] = total_elements
    result['storytelling_density'] = total_elements / len(lines) if lines else 0

    # Determine storytelling strength
    if result['storytelling_density'] > 0.6:
        result['storytelling_strength'] = "Excellent"
    elif result['storytelling_density'] > 0.4:
        result['storytelling_strength'] = "Good"
    elif result['storytelling_density'] > 0.2:
        result['storytelling_strength'] = "Moderate"
    else:
        result['storytelling_strength'] = "Weak"

    return result

# Add genre classification function
def classify_genre(text):
    # Convert text to lowercase for easier matching
    text_lower = text.lower()
    
    # Define genre keywords and patterns
    genre_indicators = {
        "Hip-Hop/Rap": ["rap", "hip hop", "flow", "beat", "rhyme", "mc", "spit", "bars", "hustle"],
        "Pop": ["love", "baby", "dance", "heart", "tonight", "feel", "party", "forever"],
        "Rock": ["rock", "roll", "guitar", "band", "wild", "hard", "heavy", "rebel"],
        "Country": ["truck", "dirt road", "beer", "hometown", "girl", "boots", "whiskey", "farm"],
        "R&B/Soul": ["soul", "feel", "baby", "love", "heart", "groove", "smooth", "rhythm"],
        "Electronic/Dance": ["beat", "drop", "bass", "club", "night", "dance", "rhythm", "electronic"],
        "Folk": ["mountain", "river", "story", "old", "tale", "land", "home", "wind"],
        "Metal": ["dark", "death", "hell", "pain", "blood", "scream", "heavy", "rage"],
        "Jazz": ["smooth", "rhythm", "soul", "groove", "blue", "note", "swing", "improvise"],
        "Blues": ["blue", "down", "lost", "feel", "soul", "gone", "road", "pain"]
    }
    
    # Count occurrences of genre indicators
    genre_scores = {}
    for genre, indicators in genre_indicators.items():
        score = sum(text_lower.count(indicator) for indicator in indicators)
        genre_scores[genre] = score
    
    # Get top 3 genres
    top_genres = sorted(genre_scores.items(), key=lambda x: x[1], reverse=True)[:3]
    
    # Calculate confidence percentages (normalize to sum to 100%)
    total_score = sum(score for _, score in top_genres) or 1  # Avoid division by zero
    genre_percentages = [(genre, (score / total_score) * 100) for genre, score in top_genres]
    
    return {
        "top_genres": genre_percentages,
        "all_scores": genre_scores
    }

# Add function to suggest similar historical works
def suggest_similar_works(analysis):
    # Dictionary of historical works by characteristics
    historical_works = {
        "Hip-Hop/Rap": [
            {"title": "The Message", "artist": "Grandmaster Flash & The Furious Five", "year": 1982, "reason": "pioneering social commentary in hip-hop"},
            {"title": "Fight the Power", "artist": "Public Enemy", "year": 1989, "reason": "politically charged lyrics and cultural impact"},
            {"title": "Juicy", "artist": "The Notorious B.I.G.", "year": 1994, "reason": "storytelling and rags-to-riches narrative"}
        ],
        "Pop": [
            {"title": "Like a Prayer", "artist": "Madonna", "year": 1989, "reason": "blending of religious and sexual themes"},
            {"title": "Billie Jean", "artist": "Michael Jackson", "year": 1983, "reason": "storytelling and rhythmic innovation"},
            {"title": "Yesterday", "artist": "The Beatles", "year": 1965, "reason": "emotional depth and simplicity"}
        ],
        "Rock": [
            {"title": "Bohemian Rhapsody", "artist": "Queen", "year": 1975, "reason": "complex structure and emotional range"},
            {"title": "Like a Rolling Stone", "artist": "Bob Dylan", "year": 1965, "reason": "poetic lyrics and cultural impact"},
            {"title": "Stairway to Heaven", "artist": "Led Zeppelin", "year": 1971, "reason": "epic structure and mystical themes"}
        ],
        "Country": [
            {"title": "Jolene", "artist": "Dolly Parton", "year": 1973, "reason": "emotional storytelling and character development"},
            {"title": "I Walk the Line", "artist": "Johnny Cash", "year": 1956, "reason": "moral themes and personal commitment"},
            {"title": "The Gambler", "artist": "Kenny Rogers", "year": 1978, "reason": "life lessons through narrative"}
        ],
        "R&B/Soul": [
            {"title": "Respect", "artist": "Aretha Franklin", "year": 1967, "reason": "powerful message and cultural impact"},
            {"title": "What's Going On", "artist": "Marvin Gaye", "year": 1971, "reason": "social commentary and emotional depth"},
            {"title": "I Heard It Through the Grapevine", "artist": "Marvin Gaye", "year": 1968, "reason": "emotional tension and narrative"}
        ],
        "Electronic/Dance": [
            {"title": "Blue Monday", "artist": "New Order", "year": 1983, "reason": "innovative structure and emotional detachment"},
            {"title": "Da Funk", "artist": "Daft Punk", "year": 1995, "reason": "minimalist lyrics with maximum impact"},
            {"title": "Strings of Life", "artist": "Rhythm is Rhythm", "year": 1987, "reason": "emotional resonance through instrumentation"}
        ],
        "Folk": [
            {"title": "Blowin' in the Wind", "artist": "Bob Dylan", "year": 1963, "reason": "poetic questioning and social themes"},
            {"title": "This Land Is Your Land", "artist": "Woody Guthrie", "year": 1940, "reason": "patriotic themes with social commentary"},
            {"title": "The Times They Are a-Changin'", "artist": "Bob Dylan", "year": 1964, "reason": "social change and generational themes"}
        ]
    }
    
    # Literary works by themes and sentiment
    literary_works = {
        "Positive": [
            {"title": "The Great Gatsby", "author": "F. Scott Fitzgerald", "year": 1925, "reason": "themes of hope and the American Dream"},
            {"title": "Pride and Prejudice", "author": "Jane Austen", "year": 1813, "reason": "wit, romance, and social commentary"},
            {"title": "Walt Whitman's Leaves of Grass", "author": "Walt Whitman", "year": 1855, "reason": "celebration of self and nature"}
        ],
        "Negative": [
            {"title": "1984", "author": "George Orwell", "year": 1949, "reason": "dystopian themes and social critique"},
            {"title": "The Road", "author": "Cormac McCarthy", "year": 2006, "reason": "post-apocalyptic despair and human resilience"},
            {"title": "Edgar Allan Poe's The Raven", "author": "Edgar Allan Poe", "year": 1845, "reason": "themes of loss and despair"}
        ],
        "Neutral": [
            {"title": "To Kill a Mockingbird", "author": "Harper Lee", "year": 1960, "reason": "moral complexity and social observation"},
            {"title": "The Old Man and the Sea", "author": "Ernest Hemingway", "year": 1952, "reason": "struggle and perseverance"},
            {"title": "Robert Frost's The Road Not Taken", "author": "Robert Frost", "year": 1916, "reason": "life choices and reflection"}
        ]
    }
    
    suggestions = []
    
    # Get top genre and sentiment
    top_genre = None
    if analysis.get('Genre_Classification', {}).get('top_genres'):
        top_genre = analysis['Genre_Classification']['top_genres'][0][0]
    
    sentiment = analysis.get('Sentiment_Analysis', {}).get('sentiment', 'Neutral')
    
    # Add musical suggestions based on genre
    if top_genre and top_genre in historical_works:
        # Get up to 2 random works from the genre
        genre_works = historical_works[top_genre]
        import random
        selected_works = random.sample(genre_works, min(2, len(genre_works)))
        
        for work in selected_works:
            suggestions.append({
                "type": "song",
                "title": work["title"],
                "creator": work["artist"],
                "year": work["year"],
                "reason": f"Similar {top_genre} style, known for {work['reason']}"
            })
    
    # Add literary suggestions based on sentiment
    if sentiment in literary_works:
        # Get up to 2 random works from the sentiment category
        sentiment_works = literary_works[sentiment]
        import random
        selected_works = random.sample(sentiment_works, min(2, len(sentiment_works)))
        
        for work in selected_works:
            suggestions.append({
                "type": "literature",
                "title": work["title"],
                "creator": work["author"],
                "year": work["year"],
                "reason": f"Similar {sentiment.lower()} tone, exploring {work['reason']}"
            })
    
    # Add suggestions based on rhyme density
    rhyme_density = analysis.get('Rhyme_Analysis', {}).get('rhyme_density', 0)
    if rhyme_density > 0.7:
        suggestions.append({
            "type": "poetry",
            "title": "Shakespeare's Sonnets",
            "creator": "William Shakespeare",
            "year": "1609",
            "reason": "High rhyme density and structured patterns similar to your lyrics"
        })
    elif rhyme_density > 0.4:
        suggestions.append({
            "type": "poetry",
            "title": "The Waste Land",
            "creator": "T.S. Eliot",
            "year": "1922",
            "reason": "Moderate rhyme patterns with deep thematic elements"
        })
    
    return suggestions

if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def extract_text(file_path):
    ext = os.path.splitext(file_path)[1].lower()
    if ext in ['.png', '.jpg', '.jpeg']:
        img = Image.open(file_path)
        text = pytesseract.image_to_string(img)
    elif ext == '.pdf':
        with open(file_path, 'rb') as file:
            pdf = PyPDF2.PdfReader(file)
            text = ''
            for page in pdf.pages:
                text += page.extract_text()
    elif ext == '.txt':
        with open(file_path, 'r') as file:
            text = file.read()
    else:
        text = ''
    return text

def analyze_lyriclens(text):
    lines = text.split('\n')
    result = {
        'Location': [], 'Actions': [], 'Thoughts': [], 'Emotions': [], 'Dialogue': [],
        'Conflict_Tension': [], 'Resolution_Payoff': [], 'Audience_Hook': [],
        'Nouns': [], 'Verbs': [], 'Adjectives': [], 'Metaphors': [], 'Rhythm_Meter': [],
        'Total_Value': 0,
        'External_Details': {'Location': [], 'Actions': [], 'Nouns': [], 'Verbs': [], 'Adjectives': []},
        'Internal_Details': {'Thoughts': [], 'Emotions': [], 'Dialogue': [], 'Resolution_Payoff': []}
    }
    
    total_value = 0
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        lower_line = line.lower()
        words = line.split()

        # Location: Specific places or settings
        location_keywords = ['in', 'at', 'on', 'from', 'england', 'floor', 'door', 'room', 'shower', 'counter', 'sofa']
        if any(word in lower_line for word in location_keywords) and not any(word in lower_line for word in ['know', 'feel', 'think']):  # Exclude internal
            if line not in result['Location']:
                result['Location'].append(line)
                result['External_Details']['Location'].append(line)

        # Actions: Full lines with action verbs
        action_keywords = ['run', 'sing', 'walk', 'do', 'say', 'creep', 'bang', 'kiss', 'catch', 'convince', 'admit', 'tell', 'forget', 'take', 'get', 'buck', 'explode', 'show', 'thrill', 'kill']
        if any(word in lower_line for word in action_keywords) and line not in result['Location']:
            if line not in result['Actions']:
                result['Actions'].append(line)
                result['External_Details']['Actions'].append(line)

        # Thoughts: Reflective or questioning lines
        thought_keywords = ['think', 'thought', 'wonder', 'realize', 'know', 'how could', 'why', 'may', 'reason', 'if']
        if any(word in lower_line for word in thought_keywords) and line not in result['Actions']:
            if line not in result['Thoughts']:
                result['Thoughts'].append(line)
                result['Internal_Details']['Thoughts'].append(line)

        # Emotions: Lines with emotional states
        emotion_keywords = ['feel', 'sorry', 'pain', 'happy', 'sad', 'angry', 'fear', 'lost', 'scream', 'alright', 'love', 'want']
        if any(word in lower_line for word in emotion_keywords) and line not in result['Thoughts']:
            if line not in result['Emotions']:
                result['Emotions'].append(line)
                result['Internal_Details']['Emotions'].append(line)

        # Dialogue: Spoken or quoted lines
        if ('"' in line or '“' in line or "'" in line or '(' in line or 'say' in lower_line) and line not in result['Thoughts']:
            if line not in result['Dialogue']:
                result['Dialogue'].append(line)
                result['Internal_Details']['Dialogue'].append(line)

        # Conflict/Tension: Lines with struggle or confrontation
        conflict_keywords = ['caught', 'cheat', 'lie', 'fight', 'struggle', 'against', 'wrong', 'mess', 'trouble']
        if any(word in lower_line for word in conflict_keywords):
            if line not in result['Conflict_Tension']:
                result['Conflict_Tension'].append(line)

        # Resolution/Payoff: Lines with outcomes or solutions
        resolution_keywords = ['sorry', 'tell', 'admit', 'end', 'fix', 'know', 'lesson', 'way', 'change', 'lost', 'get']
        if any(word in lower_line for word in resolution_keywords) and line not in result['Emotions']:
            if line not in result['Resolution_Payoff']:
                result['Resolution_Payoff'].append(line)
                result['Internal_Details']['Resolution_Payoff'].append(line)

        # Audience Hook: Short, catchy lines
        hook_keywords = ['me', 'you', 'no', 'yes', 'hey', 'oh', 'why', 'baby']
        if (len(words) < 6 and any(word in lower_line for word in hook_keywords)) or 'loverman' in lower_line:
            if line not in result['Audience_Hook']:
                result['Audience_Hook'].append(line)

        # Nouns: Individual words (tangible/abstract)
        noun_keywords = ['man', 'girl', 'door', 'floor', 'room', 'counter', 'sofa', 'shower', 'marks', 'camera', 'words', 'pain', 'style', 'dust', 'trip', 'bomb', 'soul', 'vibe', 'action', 'motion', 'england', 'ranks', 'lover']
        for word in words:
            if word.lower() in noun_keywords and word not in [n.lower() for n in result['Nouns']]:
                result['Nouns'].append(word)
                result['External_Details']['Nouns'].append(word)
                total_value += 5

        # Verbs: Individual words (action/energy)
        verb_keywords = ['run', 'sing', 'walk', 'do', 'say', 'creep', 'bang', 'kiss', 'catch', 'convince', 'admit', 'tell', 'forget', 'get', 'heard', 'saw', 'take', 'buck', 'explode', 'show', 'thrill', 'kill', 'looking', 'satisfy', 'want', 'make', 'flowing', 'claim']
        for word in words:
            if word.lower() in verb_keywords and word not in [v.lower() for v in result['Verbs']]:
                result['Verbs'].append(word)
                result['External_Details']['Verbs'].append(word)
                total_value += 50

        # Adjectives: Individual words (qualifiers)
        adj_keywords = ['real', 'hard', 'soft', 'fast', 'red', 'next', 'sorry', 'lost', 'loud', 'right', 'easy', 'full', 'strong']
        for word in words:
            if word.lower() in adj_keywords and word not in [a.lower() for a in result['Adjectives']]:
                result['Adjectives'].append(word)
                result['External_Details']['Adjectives'].append(word)
                total_value += 5

        # Metaphors: Figurative lines
        metaphor_keywords = ['like', 'as', 'explode', 'thrill', 'kill', 'flowing']
        if ' is ' in lower_line or ' of ' in lower_line or '’s ' in lower_line or any(word in lower_line for word in metaphor_keywords):
            if line not in result['Metaphors']:
                result['Metaphors'].append(line)
                total_value += 50

        # Rhythm/Meter: Syllables per line
        syllable_count = sum(1 for char in line if char.lower() in 'aeiou')
        if syllable_count > 0:
            result['Rhythm_Meter'].append(f"{line} ({syllable_count} syllables)")
            total_value += 5

    result['Total_Value'] = total_value
    
    # Add sentiment analysis to the result
    sentiment_data = analyze_sentiment(text)
    result['Sentiment_Analysis'] = sentiment_data
    
    # Add rhyme pattern detection
    rhyme_data = detect_rhyme_patterns(text)
    result['Rhyme_Analysis'] = rhyme_data
    
    # Add genre classification
    genre_data = classify_genre(text)
    result['Genre_Classification'] = genre_data
    
    # Add storytelling analysis
    storytelling_data = analyze_storytelling(text)
    result['Storytelling_Analysis'] = storytelling_data

    # Add similar works suggestions
    result['Similar_Works'] = suggest_similar_works(result)

    return result

# API Endpoints
@app.route('/api/analyze', methods=['POST'])
@api_key_required
def api_analyze():
    """API endpoint for lyric analysis"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Text is required"}), 400

        text = data['text']
        if not text.strip():
            return jsonify({"error": "Text cannot be empty"}), 400

        # Perform standard analysis
        analysis = analyze_lyriclens(text)

        # Add AI enhancement if requested
        if data.get('ai_enhanced', False):
            ai_provider = data.get('ai_provider', 'grok')  # default to grok

            # Main analysis prompt
            analysis_prompt = f"""
            Analyze these lyrics and provide insights on:
            1. Overall quality and effectiveness
            2. Strengths and weaknesses
            3. Suggestions for improvement
            4. Emotional impact assessment
            5. Commercial potential

            Lyrics:
            {text}

            Please provide a structured analysis in JSON format.
            """

            # Artist detection prompt
            artist_prompt = f"""
            Based on these lyrics, which artist(s) do they most sound like? Consider style, themes, vocabulary, and emotional expression.

            Lyrics: {text}

            Provide top 3 artist matches with confidence scores and reasoning in JSON format.
            """

            if ai_provider.lower() == 'grok':
                ai_response = call_grok_api(analysis_prompt)
                artist_response = call_grok_api(artist_prompt)
            else:
                ai_response = call_deepseek_api(analysis_prompt)
                artist_response = call_deepseek_api(artist_prompt)

            analysis['AI_Analysis'] = ai_response
            analysis['AI_Artist_Detection'] = artist_response

        return jsonify({
            "status": "success",
            "data": analysis
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/enhance', methods=['POST'])
@api_key_required
def api_enhance():
    """API endpoint for AI-powered lyric enhancement suggestions"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Text is required"}), 400

        text = data['text']
        ai_provider = data.get('ai_provider', 'grok')
        focus_area = data.get('focus_area', 'general')  # general, rhyme, story, emotion

        prompts = {
            'general': f"Provide 5 specific suggestions to improve these lyrics overall:\n\n{text}",
            'rhyme': f"Suggest improvements to the rhyme scheme and flow of these lyrics:\n\n{text}",
            'story': f"How can the storytelling in these lyrics be enhanced? Provide specific suggestions:\n\n{text}",
            'emotion': f"Suggest ways to increase the emotional impact of these lyrics:\n\n{text}"
        }

        prompt = prompts.get(focus_area, prompts['general'])

        if ai_provider.lower() == 'grok':
            ai_response = call_grok_api(prompt)
        else:
            ai_response = call_deepseek_api(prompt)

        return jsonify({
            "status": "success",
            "focus_area": focus_area,
            "suggestions": ai_response
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/compare-style', methods=['POST'])
@api_key_required
def api_compare_style():
    """API endpoint to compare lyrics to famous artists/styles"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Text is required"}), 400

        text = data['text']
        target_artist = data.get('artist', '')
        ai_provider = data.get('ai_provider', 'grok')

        if target_artist:
            prompt = f"""
            Compare these lyrics to the style of {target_artist}. Analyze:
            1. Similarities in style, themes, and approach
            2. Differences and unique elements
            3. How well it captures {target_artist}'s essence
            4. Suggestions to make it more like {target_artist}'s style

            Lyrics:
            {text}
            """
        else:
            prompt = f"""
            Analyze these lyrics and identify which famous artists or musical styles they most resemble.
            Provide the top 3 matches with explanations:

            Lyrics:
            {text}
            """

        if ai_provider.lower() == 'grok':
            ai_response = call_grok_api(prompt)
        else:
            ai_response = call_deepseek_api(prompt)

        return jsonify({
            "status": "success",
            "target_artist": target_artist,
            "style_analysis": ai_response
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/generate-rhymes', methods=['POST'])
@api_key_required
def api_generate_rhymes():
    """API endpoint for AI-powered rhyme suggestions"""
    try:
        data = request.get_json()
        if not data or 'word' not in data:
            return jsonify({"error": "Word is required"}), 400

        word = data['word']
        context = data.get('context', '')
        ai_provider = data.get('ai_provider', 'grok')

        prompt = f"""
        Generate creative rhymes for the word "{word}".
        Context: {context}

        Provide:
        1. Perfect rhymes (10 words)
        2. Near rhymes/slant rhymes (10 words)
        3. Creative multi-syllable rhymes (5 phrases)
        4. Rhymes that fit the context/theme

        Format as a structured list.
        """

        if ai_provider.lower() == 'grok':
            ai_response = call_grok_api(prompt)
        else:
            ai_response = call_deepseek_api(prompt)

        return jsonify({
            "status": "success",
            "word": word,
            "context": context,
            "rhymes": ai_response
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/detect-artist', methods=['POST'])
@api_key_required
def api_detect_artist():
    """API endpoint for AI-powered artist detection"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Text is required"}), 400

        text = data['text']
        ai_provider = data.get('ai_provider', 'deepseek')
        confidence_threshold = data.get('confidence_threshold', 0.7)

        prompt = f"""
        Analyze these lyrics and identify which artist(s) they most likely sound like. Consider:

        1. **Lyrical Style & Themes**: Word choice, subject matter, emotional tone
        2. **Narrative Approach**: Storytelling style, perspective, imagery
        3. **Vocabulary & Language**: Complexity, slang, metaphors, cultural references
        4. **Emotional Expression**: How feelings are conveyed, vulnerability level
        5. **Song Structure Hints**: Verse patterns, hook potential, repetition

        Lyrics to analyze:
        "{text}"

        Provide your analysis in this JSON format:
        {{
            "primary_match": {{
                "artist": "Artist Name",
                "confidence": 0.85,
                "reasoning": "Detailed explanation of why this artist matches"
            }},
            "secondary_matches": [
                {{
                    "artist": "Artist Name 2",
                    "confidence": 0.72,
                    "reasoning": "Why this is also a good match"
                }}
            ],
            "style_characteristics": [
                "Characteristic 1",
                "Characteristic 2"
            ],
            "genre_indicators": ["Pop", "R&B"],
            "era_suggestion": "2020s",
            "overall_analysis": "Summary of the lyrical style and artist detection reasoning"
        }}

        Be specific about WHY you think it matches each artist. Consider both mainstream and emerging artists.
        """

        if ai_provider.lower() == 'grok':
            ai_response = call_grok_api(prompt, max_tokens=1200)
        else:
            ai_response = call_deepseek_api(prompt, max_tokens=1200)

        return jsonify({
            "status": "success",
            "artist_detection": ai_response,
            "confidence_threshold": confidence_threshold,
            "analysis_provider": ai_provider
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/story-feedback', methods=['POST'])
@api_key_required
def api_story_feedback():
    """API endpoint for storytelling analysis and feedback"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Text is required"}), 400

        text = data['text']
        ai_provider = data.get('ai_provider', 'grok')

        # First get our storytelling analysis
        storytelling_analysis = analyze_storytelling(text)

        prompt = f"""
        Analyze the storytelling elements in these lyrics using the 5-element framework:
        1. Location (where you are)
        2. Actions (what you're doing)
        3. Thoughts (what you're thinking)
        4. Emotions (what you're feeling)
        5. Dialogue (what you're hearing)

        Current analysis shows:
        - Storytelling strength: {storytelling_analysis['storytelling_strength']}
        - Density: {storytelling_analysis['storytelling_density']:.1%}
        - Elements found: {storytelling_analysis['storytelling_score']}

        Lyrics:
        {text}

        Provide specific feedback on:
        1. Which storytelling elements are strong/weak
        2. Specific suggestions to improve the narrative
        3. How to make the story more engaging
        4. Examples of how to enhance weak areas
        """

        if ai_provider.lower() == 'grok':
            ai_response = call_grok_api(prompt, max_tokens=1500)
        else:
            ai_response = call_deepseek_api(prompt, max_tokens=1500)

        return jsonify({
            "status": "success",
            "storytelling_analysis": storytelling_analysis,
            "ai_feedback": ai_response
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def api_health():
    """API health check endpoint"""
    return jsonify({
        "status": "healthy",
        "version": "1.0.0",
        "features": {
            "grok_available": bool(GROK_API_KEY),
            "deepseek_available": bool(DEEPSEEK_API_KEY)
        }
    })

@app.route('/api/docs')
def api_docs():
    """API documentation page"""
    return render_template('api_docs.html')

@app.route('/ai/test')
def ai_test():
    """AI features test page"""
    return render_template('ai_test.html')

@app.route('/test/simple')
def simple_test():
    """Simple API test page"""
    return render_template('simple_test.html')

@app.route('/landing')
def landing():
    """Landing page"""
    return render_template('landing.html')

@app.route('/home')
def home_redirect():
    """Redirect /home to landing page"""
    return redirect(url_for('landing'))

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        if 'file' in request.files and request.files['file'].filename != '':
            file = request.files['file']
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(file_path)
            text = extract_text(file_path)
        elif 'text' in request.form and request.form['text'].strip():
            text = request.form['text']
        else:
            return render_template('index.html', error="Please upload a file or paste text.")
        
        analysis = analyze_lyriclens(text)
        
        # If user is logged in, offer to save the analysis
        if current_user.is_authenticated:
            return render_template('index.html', analysis=analysis, input_text=text, can_save=True)
        else:
            return render_template('index.html', analysis=analysis, input_text=text)
    
    return render_template('index.html')

@app.route('/save_analysis', methods=['POST'])
@login_required
def save_analysis():
    title = request.form.get('title')
    text = request.form.get('input_text')
    analysis_data = request.form.get('analysis_data')
    public = 'public' in request.form
    
    if not title or not text or not analysis_data:
        flash('Missing required data', 'danger')
        return redirect(url_for('dashboard'))
    
    # Create new analysis
    analysis = Analysis(
        title=title,
        text=text,
        result=analysis_data,
        public=public,
        user_id=current_user.id
    )
    
    db.session.add(analysis)
    db.session.commit()
    
    flash('Analysis saved successfully!', 'success')
    return redirect(url_for('dashboard'))

@app.route('/analysis/<int:analysis_id>')
def view_analysis(analysis_id):
    analysis = Analysis.query.get_or_404(analysis_id)
    
    # Check if user has access to this analysis
    if not analysis.public:
        if not current_user.is_authenticated:
            flash('You need to log in to view this analysis', 'danger')
            return redirect(url_for('login', next=request.url))
        
        if analysis.user_id != current_user.id:
            # Check if it's shared with the current user
            share = Share.query.filter_by(
                analysis_id=analysis.id,
                shared_with_id=current_user.id
            ).first()
            
            if not share:
                flash('You do not have permission to view this analysis', 'danger')
                return redirect(url_for('dashboard'))
    
    # Parse the JSON result
    analysis_result = json.loads(analysis.result)
    
    return render_template('view_analysis.html', 
                          analysis=analysis, 
                          analysis_result=analysis_result,
                          input_text=analysis.text)

@app.route('/share/<int:analysis_id>', methods=['GET', 'POST'])
@login_required
def share_analysis(analysis_id):
    analysis = Analysis.query.get_or_404(analysis_id)
    
    # Ensure the current user owns this analysis
    if analysis.user_id != current_user.id:
        flash('You can only share analyses that you own', 'danger')
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        can_edit = 'can_edit' in request.form
        
        # Find the user to share with
        user = User.query.filter_by(username=username).first()
        
        if not user:
            flash(f'User {username} not found', 'danger')
            return redirect(url_for('share_analysis', analysis_id=analysis_id))
        
        # Don't share with yourself
        if user.id == current_user.id:
            flash('You cannot share an analysis with yourself', 'danger')
            return redirect(url_for('share_analysis', analysis_id=analysis_id))
        
        # Check if already shared
        existing_share = Share.query.filter_by(
            analysis_id=analysis_id,
            shared_with_id=user.id
        ).first()
        
        if existing_share:
            # Update sharing permissions
            existing_share.can_edit = can_edit
            db.session.commit()
            flash(f'Updated sharing permissions for {username}', 'success')
        else:
            # Create new share
            share = Share(
                analysis_id=analysis_id,
                shared_with_id=user.id,
                can_edit=can_edit
            )
            db.session.add(share)
            db.session.commit()
            flash(f'Analysis shared with {username}', 'success')
        
        return redirect(url_for('share_analysis', analysis_id=analysis_id))
    
    # Get current shares
    shares = Share.query.filter_by(analysis_id=analysis_id).all()
    
    return render_template('share.html', analysis=analysis, shares=shares)

@app.route('/remove_share/<int:share_id>', methods=['POST'])
@login_required
def remove_share(share_id):
    share = Share.query.get_or_404(share_id)
    analysis = Analysis.query.get_or_404(share.analysis_id)
    
    # Ensure the current user owns this analysis
    if analysis.user_id != current_user.id:
        flash('You can only manage shares for analyses that you own', 'danger')
        return redirect(url_for('dashboard'))
    
    username = share.shared_with.username
    db.session.delete(share)
    db.session.commit()
    
    flash(f'Removed sharing with {username}', 'success')
    return redirect(url_for('share_analysis', analysis_id=analysis.id))

@app.route('/compare', methods=['POST'])
def compare_lyrics():
    # Get text from both inputs
    text_a = ""
    text_b = ""
    
    # Process first set of lyrics
    if 'file_a' in request.files and request.files['file_a'].filename != '':
        file_a = request.files['file_a']
        file_path_a = os.path.join(app.config['UPLOAD_FOLDER'], file_a.filename)
        file_a.save(file_path_a)
        text_a = extract_text(file_path_a)
    elif 'text_a' in request.form and request.form['text_a'].strip():
        text_a = request.form['text_a']
    else:
        return render_template('index.html', error="Please provide content for Lyrics A.")
    
    # Process second set of lyrics
    if 'file_b' in request.files and request.files['file_b'].filename != '':
        file_b = request.files['file_b']
        file_path_b = os.path.join(app.config['UPLOAD_FOLDER'], file_b.filename)
        file_b.save(file_path_b)
        text_b = extract_text(file_path_b)
    elif 'text_b' in request.form and request.form['text_b'].strip():
        text_b = request.form['text_b']
    else:
        return render_template('index.html', error="Please provide content for Lyrics B.")
    
    # Analyze both sets of lyrics
    analysis_a = analyze_lyriclens(text_a)
    analysis_b = analyze_lyriclens(text_b)
    
    # Add input text to the analysis results for display
    analysis_a['input_text'] = text_a
    analysis_b['input_text'] = text_b
    
    return render_template('compare.html', analysis_a=analysis_a, analysis_b=analysis_b)

def generate_smart_filename(analysis, base_name="lyriclens_analysis"):
    """Generate intelligent filename based on AI artist detection"""

    # First try AI artist detection
    if 'AI_Artist_Detection' in analysis:
        try:
            ai_response = analysis['AI_Artist_Detection']
            if ai_response and 'choices' in ai_response and ai_response['choices']:
                content = ai_response['choices'][0]['message']['content']

                # Extract artist name from JSON response
                import re
                artist_match = re.search(r'"artist":\s*"([^"]+)"', content)
                if artist_match:
                    artist_name = artist_match.group(1).lower()
                    # Clean the artist name for filename
                    clean_name = re.sub(r'[^a-z0-9\s]', '', artist_name)
                    clean_name = re.sub(r'\s+', '_', clean_name)
                    return f"{clean_name}_style_analysis"
        except:
            pass

    # Fallback to similar works
    if 'Similar_Works' in analysis and analysis['Similar_Works']:
        try:
            first_work = analysis['Similar_Works'][0]
            if 'creator' in first_work:
                artist_name = first_work['creator'].lower()
                clean_name = re.sub(r'[^a-z0-9\s]', '', artist_name)
                clean_name = re.sub(r'\s+', '_', clean_name)
                return f"{clean_name}_style_analysis"
        except:
            pass

    # Default filename
    return base_name

@app.route('/export/<format>', methods=['POST'])
def export_analysis(format):
    # Get analysis data from form
    analysis_data = request.form.get('analysis_data')
    input_text = request.form.get('input_text')

    if not analysis_data:
        return "No analysis data provided", 400
    
    # Parse the JSON string back to a dictionary
    analysis = json.loads(analysis_data)

    # Generate smart filename based on AI detection
    base_filename = generate_smart_filename(analysis)

    if format == 'json':
        # Return JSON file
        return Response(
            analysis_data,
            mimetype='application/json',
            headers={'Content-Disposition': f'attachment;filename={base_filename}.json'}
        )
    
    elif format == 'csv':
        # Create CSV file
        output = StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow(['LyricLens+ Analysis Results'])
        writer.writerow([''])
        
        # Write input text
        writer.writerow(['Input Text'])
        for line in input_text.split('\n'):
            writer.writerow([line])
        writer.writerow([''])
        
        # Write analysis sections
        sections = [
            ('External Details - Location', analysis.get('External_Details', {}).get('Location', [])),
            ('External Details - Actions', analysis.get('External_Details', {}).get('Actions', [])),
            ('External Details - Nouns', analysis.get('External_Details', {}).get('Nouns', [])),
            ('External Details - Verbs', analysis.get('External_Details', {}).get('Verbs', [])),
            ('External Details - Adjectives', analysis.get('External_Details', {}).get('Adjectives', [])),
            ('Internal Details - Thoughts', analysis.get('Internal_Details', {}).get('Thoughts', [])),
            ('Internal Details - Emotions', analysis.get('Internal_Details', {}).get('Emotions', [])),
            ('Internal Details - Dialogue', analysis.get('Internal_Details', {}).get('Dialogue', [])),
            ('Internal Details - Resolution/Payoff', analysis.get('Internal_Details', {}).get('Resolution_Payoff', [])),
            ('Conflict/Tension', analysis.get('Conflict_Tension', [])),
            ('Audience Hook', analysis.get('Audience_Hook', [])),
            ('Metaphors', analysis.get('Metaphors', [])),
        ]
        
        for section_name, items in sections:
            writer.writerow([section_name])
            for item in items:
                writer.writerow([item])
            writer.writerow([''])
        
        # Write sentiment analysis
        sentiment = analysis.get('Sentiment_Analysis', {})
        writer.writerow(['Sentiment Analysis'])
        writer.writerow(['Overall Sentiment', sentiment.get('sentiment', '')])
        writer.writerow(['Polarity Score', sentiment.get('polarity', '')])
        writer.writerow(['Subjectivity Score', sentiment.get('subjectivity', '')])
        writer.writerow([''])
        
        # Write rhyme analysis
        rhyme = analysis.get('Rhyme_Analysis', {})
        writer.writerow(['Rhyme Analysis'])
        writer.writerow(['Rhyme Density', f"{rhyme.get('rhyme_density', 0) * 100:.1f}%"])
        writer.writerow([''])
        writer.writerow(['Annotated Lines with Rhyme Scheme'])
        for line in rhyme.get('annotated_lines', []):
            writer.writerow([line])
        writer.writerow([''])
        
        # Write genre classification
        genre = analysis.get('Genre_Classification', {})
        writer.writerow(['Genre Classification'])
        for g, p in genre.get('top_genres', []):
            writer.writerow([g, f"{p:.1f}%"])
        writer.writerow([''])

        # Write storytelling analysis
        storytelling = analysis.get('Storytelling_Analysis', {})
        writer.writerow(['Storytelling Analysis'])
        writer.writerow(['Storytelling Strength', storytelling.get('storytelling_strength', '')])
        writer.writerow(['Storytelling Density', f"{storytelling.get('storytelling_density', 0) * 100:.1f}%"])
        writer.writerow(['Total Elements', storytelling.get('storytelling_score', 0)])
        writer.writerow([''])

        storytelling_sections = [
            ('Location Elements', storytelling.get('Location', [])),
            ('Action Elements', storytelling.get('Actions', [])),
            ('Thought Elements', storytelling.get('Thoughts', [])),
            ('Emotion Elements', storytelling.get('Emotions', [])),
            ('Dialogue Elements', storytelling.get('Dialogue', []))
        ]

        for section_name, items in storytelling_sections:
            writer.writerow([section_name])
            for item in items:
                writer.writerow([item])
            writer.writerow([''])
        
        # Return CSV file
        output.seek(0)
        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment;filename={base_filename}.csv'}
        )
    
    elif format == 'txt':
        # Create plain text file
        output = StringIO()

        # Write header
        output.write("LyricLens+ Analysis Results\n")
        output.write("=" * 50 + "\n\n")

        # Write input text
        output.write("INPUT TEXT:\n")
        output.write("-" * 20 + "\n")
        output.write(input_text + "\n\n")

        # Write analysis sections
        sections = [
            ('EXTERNAL DETAILS - LOCATION', analysis.get('External_Details', {}).get('Location', [])),
            ('EXTERNAL DETAILS - ACTIONS', analysis.get('External_Details', {}).get('Actions', [])),
            ('EXTERNAL DETAILS - NOUNS', analysis.get('External_Details', {}).get('Nouns', [])),
            ('EXTERNAL DETAILS - VERBS', analysis.get('External_Details', {}).get('Verbs', [])),
            ('EXTERNAL DETAILS - ADJECTIVES', analysis.get('External_Details', {}).get('Adjectives', [])),
            ('INTERNAL DETAILS - THOUGHTS', analysis.get('Internal_Details', {}).get('Thoughts', [])),
            ('INTERNAL DETAILS - EMOTIONS', analysis.get('Internal_Details', {}).get('Emotions', [])),
            ('INTERNAL DETAILS - DIALOGUE', analysis.get('Internal_Details', {}).get('Dialogue', [])),
            ('INTERNAL DETAILS - RESOLUTION/PAYOFF', analysis.get('Internal_Details', {}).get('Resolution_Payoff', [])),
            ('CONFLICT/TENSION', analysis.get('Conflict_Tension', [])),
            ('AUDIENCE HOOK', analysis.get('Audience_Hook', [])),
            ('METAPHORS', analysis.get('Metaphors', [])),
        ]

        for section_name, items in sections:
            output.write(f"{section_name}:\n")
            output.write("-" * len(section_name) + "\n")
            if items:
                for item in items:
                    output.write(f"• {item}\n")
            else:
                output.write("None detected\n")
            output.write("\n")

        # Write sentiment analysis
        sentiment = analysis.get('Sentiment_Analysis', {})
        output.write("SENTIMENT ANALYSIS:\n")
        output.write("-" * 18 + "\n")
        output.write(f"Overall Sentiment: {sentiment.get('sentiment', '')}\n")
        output.write(f"Polarity Score: {sentiment.get('polarity', '')}\n")
        output.write(f"Subjectivity Score: {sentiment.get('subjectivity', '')}\n\n")

        # Write rhyme analysis
        rhyme = analysis.get('Rhyme_Analysis', {})
        output.write("RHYME ANALYSIS:\n")
        output.write("-" * 14 + "\n")
        output.write(f"Rhyme Density: {rhyme.get('rhyme_density', 0) * 100:.1f}%\n\n")
        output.write("Annotated Lines with Rhyme Scheme:\n")
        for line in rhyme.get('annotated_lines', []):
            output.write(f"{line}\n")
        output.write("\n")

        # Write genre classification
        genre = analysis.get('Genre_Classification', {})
        output.write("GENRE CLASSIFICATION:\n")
        output.write("-" * 20 + "\n")
        for g, p in genre.get('top_genres', []):
            output.write(f"{g}: {p:.1f}%\n")
        output.write("\n")

        # Write storytelling analysis
        storytelling = analysis.get('Storytelling_Analysis', {})
        output.write("STORYTELLING ANALYSIS:\n")
        output.write("-" * 22 + "\n")
        output.write(f"Storytelling Strength: {storytelling.get('storytelling_strength', '')}\n")
        output.write(f"Storytelling Density: {storytelling.get('storytelling_density', 0) * 100:.1f}%\n")
        output.write(f"Total Elements: {storytelling.get('storytelling_score', 0)}\n\n")

        storytelling_sections = [
            ('LOCATION ELEMENTS', storytelling.get('Location', [])),
            ('ACTION ELEMENTS', storytelling.get('Actions', [])),
            ('THOUGHT ELEMENTS', storytelling.get('Thoughts', [])),
            ('EMOTION ELEMENTS', storytelling.get('Emotions', [])),
            ('DIALOGUE ELEMENTS', storytelling.get('Dialogue', []))
        ]

        for section_name, items in storytelling_sections:
            output.write(f"{section_name}:\n")
            output.write("-" * len(section_name) + "\n")
            if items:
                for item in items:
                    output.write(f"• {item}\n")
            else:
                output.write("None detected\n")
            output.write("\n")

        output.write(f"Total Value: {analysis.get('Total_Value', 0)} cents (${analysis.get('Total_Value', 0) / 100:.2f})\n")

        # Return text file
        return Response(
            output.getvalue(),
            mimetype='text/plain',
            headers={'Content-Disposition': f'attachment;filename={base_filename}.txt'}
        )

    elif format == 'docx':
        # For now, return as rich text format (RTF) which can be opened by Word
        output = StringIO()

        # RTF header
        output.write(r'{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}')
        output.write(r'\f0\fs24 ')

        # Title
        output.write(r'{\b\fs32 LyricLens+ Analysis Results}\par\par')

        # Input text
        output.write(r'{\b\fs28 Input Text:}\par')
        output.write(input_text.replace('\n', r'\par ') + r'\par\par')

        # Analysis sections (simplified for RTF)
        output.write(r'{\b\fs28 Analysis Summary:}\par')
        output.write(f"Total Value: {analysis.get('Total_Value', 0)} cents\\par")

        sentiment = analysis.get('Sentiment_Analysis', {})
        output.write(f"Sentiment: {sentiment.get('sentiment', '')}\\par")

        rhyme = analysis.get('Rhyme_Analysis', {})
        output.write(f"Rhyme Density: {rhyme.get('rhyme_density', 0) * 100:.1f}%\\par")

        # RTF footer
        output.write('}')

        return Response(
            output.getvalue(),
            mimetype='application/rtf',
            headers={'Content-Disposition': f'attachment;filename={base_filename}.rtf'}
        )

    elif format == 'png':
        # For PNG export, we'll create a simple image with key metrics
        # This requires additional image processing libraries
        return Response(
            "PNG export feature coming soon! Please use PDF export for now.",
            mimetype='text/plain',
            status=501
        )

    elif format == 'pdf':
        # Create PDF file
        buffer = BytesIO()  # Changed from io.BytesIO() to BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        styles = getSampleStyleSheet()
        elements = []
        
        # Add title
        title_style = styles['Title']
        elements.append(Paragraph("LyricLens+ Analysis Results", title_style))
        elements.append(Spacer(1, 12))
        
        # Add input text
        elements.append(Paragraph("Input Text:", styles['Heading2']))
        elements.append(Paragraph(input_text.replace('\n', '<br/>'), styles['Normal']))
        elements.append(Spacer(1, 12))
        
        # Add analysis sections
        section_style = styles['Heading2']
        subsection_style = styles['Heading3']
        normal_style = styles['Normal']
        
        # External Details
        elements.append(Paragraph("External Details:", section_style))
        
        # Location
        locations = analysis.get('External_Details', {}).get('Location', [])
        if locations:
            elements.append(Paragraph("Location:", subsection_style))
            for item in locations:
                elements.append(Paragraph(f"• {item}", normal_style))
            elements.append(Spacer(1, 6))
        
        # Actions
        actions = analysis.get('External_Details', {}).get('Actions', [])
        if actions:
            elements.append(Paragraph("Actions:", subsection_style))
            for item in actions:
                elements.append(Paragraph(f"• {item}", normal_style))
            elements.append(Spacer(1, 6))
        
        # Nouns, Verbs, Adjectives
        for label, key in [
            ("Nouns (5¢ each):", "Nouns"),
            ("Verbs (50¢ each):", "Verbs"),
            ("Adjectives (5¢ each):", "Adjectives")
        ]:
            items = analysis.get('External_Details', {}).get(key, [])
            if items:
                elements.append(Paragraph(label, subsection_style))
                elements.append(Paragraph(", ".join(items), normal_style))
                elements.append(Spacer(1, 6))
        
        elements.append(Spacer(1, 12))
        
        # Internal Details
        elements.append(Paragraph("Internal Details:", section_style))
        
        for label, key in [
            ("Thoughts:", "Thoughts"),
            ("Emotions:", "Emotions"),
            ("Dialogue:", "Dialogue"),
            ("Resolution/Payoff:", "Resolution_Payoff")
        ]:
            items = analysis.get('Internal_Details', {}).get(key, [])
            if items:
                elements.append(Paragraph(label, subsection_style))
                for item in items:
                    elements.append(Paragraph(f"• {item}", normal_style))
                elements.append(Spacer(1, 6))
        
        elements.append(Spacer(1, 12))
        
        # Additional ESVAF Elements
        elements.append(Paragraph("Additional ESVAF Elements:", section_style))
        
        for label, key in [
            ("Conflict/Tension:", "Conflict_Tension"),
            ("Audience Hook:", "Audience_Hook"),
            ("Metaphors (50¢ each):", "Metaphors")
        ]:
            items = analysis.get(key, [])
            if items:
                elements.append(Paragraph(label, subsection_style))
                for item in items:
                    elements.append(Paragraph(f"• {item}", normal_style))
                elements.append(Spacer(1, 6))
        
        # Total Value
        elements.append(Paragraph(f"Total Value: {analysis.get('Total_Value', 0)} cents (${analysis.get('Total_Value', 0) / 100:.2f})", subsection_style))
        elements.append(Spacer(1, 12))
        
        # Sentiment Analysis
        sentiment = analysis.get('Sentiment_Analysis', {})
        if sentiment:
            elements.append(Paragraph("Sentiment Analysis:", section_style))
            elements.append(Paragraph(f"Overall Sentiment: {sentiment.get('sentiment', '')}", normal_style))
            elements.append(Paragraph(f"Polarity Score: {sentiment.get('polarity', 0):.2f} (Range: -1 to 1, where -1 is negative, 0 is neutral, 1 is positive)", normal_style))
            elements.append(Paragraph(f"Subjectivity Score: {sentiment.get('subjectivity', 0):.2f} (Range: 0 to 1, where 0 is objective, 1 is subjective)", normal_style))
            elements.append(Spacer(1, 12))
        
        # Rhyme Analysis
        rhyme = analysis.get('Rhyme_Analysis', {})
        if rhyme:
            elements.append(Paragraph("Rhyme Pattern Analysis:", section_style))
            elements.append(Paragraph(f"Rhyme Density: {rhyme.get('rhyme_density', 0) * 100:.1f}% of lines contain rhymes", normal_style))
            elements.append(Spacer(1, 6))
            
            elements.append(Paragraph("Annotated Lines with Rhyme Scheme:", subsection_style))
            for line in rhyme.get('annotated_lines', []):
                elements.append(Paragraph(line, normal_style))
            elements.append(Spacer(1, 12))
        
        # Genre Classification
        genre = analysis.get('Genre_Classification', {})
        if genre:
            elements.append(Paragraph("Genre Classification:", section_style))
            data = [["Genre", "Confidence"]]
            for g, p in genre.get('top_genres', []):
                data.append([g, f"{p:.1f}%"])
            
            if len(data) > 1:
                table = Table(data, colWidths=[300, 100])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.purple),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lavender),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(table)
            
            elements.append(Spacer(1, 6))
            elements.append(Paragraph("Note: Genre classification is based on keyword analysis and is meant as a suggestion only. Many songs cross multiple genres or create new ones!", normal_style))
        
        # Add similar works
        if analysis.get('Similar_Works'):
            elements.append(Paragraph("Similar Historical Works:", section_style))
            for work in analysis.get('Similar_Works', []):
                elements.append(Paragraph(f"{work.get('title')} ({work.get('year')}) by {work.get('creator')}", subsection_style))
                elements.append(Paragraph(f"Type: {work.get('type').capitalize()}", normal_style))
                elements.append(Paragraph(f"Reason: {work.get('reason')}", normal_style))
                elements.append(Spacer(1, 6))
        
        # Build PDF
        doc.build(elements)
        buffer.seek(0)
        
        # Return PDF file
        return send_file(
            buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'{base_filename}.pdf'
        )
    
    else:
        return "Invalid export format", 400

@app.route('/export_comparison/<format>', methods=['POST'])
def export_comparison(format):
    # Get analysis data from form
    analysis_data_a = request.form.get('analysis_data_a')
    analysis_data_b = request.form.get('analysis_data_b')
    
    if not analysis_data_a or not analysis_data_b:
        return "Missing analysis data", 400
    
    # Parse the JSON strings back to dictionaries
    analysis_a = json.loads(analysis_data_a)
    analysis_b = json.loads(analysis_data_b)
    
    if format == 'pdf':
        # Create PDF file
        buffer = BytesIO()  # Changed from io.BytesIO() to BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        styles = getSampleStyleSheet()
        elements = []
        
        # Add title
        title_style = styles['Title']
        elements.append(Paragraph("LyricLens+ Lyrics Comparison", title_style))
        elements.append(Spacer(1, 12))
        
        # Create comparison table
        data = [
            ["", "Lyrics A", "Lyrics B"],
            ["Input Text", analysis_a.get('input_text', ''), analysis_b.get('input_text', '')],
            ["Sentiment", 
             f"{analysis_a.get('Sentiment_Analysis', {}).get('sentiment', '')}\nPolarity: {analysis_a.get('Sentiment_Analysis', {}).get('polarity', 0):.2f}", 
             f"{analysis_b.get('Sentiment_Analysis', {}).get('sentiment', '')}\nPolarity: {analysis_b.get('Sentiment_Analysis', {}).get('polarity', 0):.2f}"],
            ["Rhyme Density", 
             f"{analysis_a.get('Rhyme_Analysis', {}).get('rhyme_density', 0) * 100:.1f}%", 
             f"{analysis_b.get('Rhyme_Analysis', {}).get('rhyme_density', 0) * 100:.1f}%"],
            ["Top Genre", 
             f"{analysis_a.get('Genre_Classification', {}).get('top_genres', [['']])[0][0]} ({analysis_a.get('Genre_Classification', {}).get('top_genres', [[0, 0]])[0][1]:.1f}%)" if analysis_a.get('Genre_Classification', {}).get('top_genres') else "None", 
             f"{analysis_b.get('Genre_Classification', {}).get('top_genres', [['']])[0][0]} ({analysis_b.get('Genre_Classification', {}).get('top_genres', [[0, 0]])[0][1]:.1f}%)" if analysis_b.get('Genre_Classification', {}).get('top_genres') else "None"],
            ["External Details", 
             f"Locations: {len(analysis_a.get('External_Details', {}).get('Location', []))}\nActions: {len(analysis_a.get('External_Details', {}).get('Actions', []))}\nNouns: {len(analysis_a.get('External_Details', {}).get('Nouns', []))}\nVerbs: {len(analysis_a.get('External_Details', {}).get('Verbs', []))}", 
             f"Locations: {len(analysis_b.get('External_Details', {}).get('Location', []))}\nActions: {len(analysis_b.get('External_Details', {}).get('Actions', []))}\nNouns: {len(analysis_b.get('External_Details', {}).get('Nouns', []))}\nVerbs: {len(analysis_b.get('External_Details', {}).get('Verbs', []))}"],
            ["Internal Details", 
             f"Thoughts: {len(analysis_a.get('Internal_Details', {}).get('Thoughts', []))}\nEmotions: {len(analysis_a.get('Internal_Details', {}).get('Emotions', []))}\nDialogue: {len(analysis_a.get('Internal_Details', {}).get('Dialogue', []))}", 
             f"Thoughts: {len(analysis_b.get('Internal_Details', {}).get('Thoughts', []))}\nEmotions: {len(analysis_b.get('Internal_Details', {}).get('Emotions', []))}\nDialogue: {len(analysis_b.get('Internal_Details', {}).get('Dialogue', []))}"],
            ["Total Value", 
             f"{analysis_a.get('Total_Value', 0)} cents (${analysis_a.get('Total_Value', 0) / 100:.2f})", 
             f"{analysis_b.get('Total_Value', 0)} cents (${analysis_b.get('Total_Value', 0) / 100:.2f})"]
        ]
        
        # Create the table
        table = Table(data, colWidths=[100, 200, 200])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.purple),
            ('BACKGROUND', (0, 1), (0, -1), colors.lavender),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (0, -1), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP')
        ]))
        elements.append(table)
        elements.append(Spacer(1, 12))
        
        # Add key insights
        elements.append(Paragraph("Key Insights:", styles['Heading2']))
        insights = []
        
        # Compare sentiment
        if analysis_a.get('Sentiment_Analysis', {}).get('polarity', 0) > analysis_b.get('Sentiment_Analysis', {}).get('polarity', 0):
            insights.append("Lyrics A has a more positive tone than Lyrics B.")
        elif analysis_b.get('Sentiment_Analysis', {}).get('polarity', 0) > analysis_a.get('Sentiment_Analysis', {}).get('polarity', 0):
            insights.append("Lyrics B has a more positive tone than Lyrics A.")
        else:
            insights.append("Both lyrics have similar emotional tones.")
        
        # Compare rhyme density
        if analysis_a.get('Rhyme_Analysis', {}).get('rhyme_density', 0) > analysis_b.get('Rhyme_Analysis', {}).get('rhyme_density', 0):
            insights.append("Lyrics A has a higher rhyme density than Lyrics B.")
        elif analysis_b.get('Rhyme_Analysis', {}).get('rhyme_density', 0) > analysis_a.get('Rhyme_Analysis', {}).get('rhyme_density', 0):
            insights.append("Lyrics B has a higher rhyme density than Lyrics A.")
        else:
            insights.append("Both lyrics have similar rhyme patterns.")
        
        # Compare total value
        if analysis_a.get('Total_Value', 0) > analysis_b.get('Total_Value', 0):
            insights.append("Lyrics A has a higher commercial value than Lyrics B.")
        elif analysis_b.get('Total_Value', 0) > analysis_a.get('Total_Value', 0):
            insights.append("Lyrics B has a higher commercial value than Lyrics A.")
        else:
            insights.append("Both lyrics have similar commercial value.")
        
        # Add insights to PDF
        for insight in insights:
            elements.append(Paragraph(f"• {insight}", styles['Normal']))
        
        # Build PDF
        doc.build(elements)
        buffer.seek(0)
        
        # Return PDF file
        return send_file(
            buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='lyriclens_comparison.pdf'
        )
    
    else:
        return "Only PDF export is supported for comparisons", 400

@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        # Validate input
        if not username or not email or not password:
            flash('All fields are required', 'danger')
            return render_template('register.html')
        
        if password != confirm_password:
            flash('Passwords do not match', 'danger')
            return render_template('register.html')
        
        # Check if username or email already exists
        if User.query.filter_by(username=username).first():
            flash('Username already exists', 'danger')
            return render_template('register.html')
        
        if User.query.filter_by(email=email).first():
            flash('Email already exists', 'danger')
            return render_template('register.html')
        
        # Create new user
        user = User(username=username, email=email)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
        
        flash('Registration successful! Please log in.', 'success')
        return redirect(url_for('login'))
    
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = 'remember' in request.form
        
        user = User.query.filter_by(username=username).first()
        
        if not user or not user.check_password(password):
            flash('Invalid username or password', 'danger')
            return render_template('login.html')
        
        login_user(user, remember=remember)
        next_page = request.args.get('next')
        return redirect(next_page or url_for('dashboard'))
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get user's analyses
    user_analyses = Analysis.query.filter_by(user_id=current_user.id).order_by(Analysis.updated_at.desc()).all()
    
    # Get analyses shared with the user
    shared_analyses = Analysis.query.join(Share).filter(Share.shared_with_id == current_user.id).all()
    
    return render_template('dashboard.html', user_analyses=user_analyses, shared_analyses=shared_analyses)

@app.route('/toggle_public/<int:analysis_id>', methods=['POST'])
@login_required
def toggle_public(analysis_id):
    analysis = Analysis.query.get_or_404(analysis_id)
    
    # Ensure the current user owns this analysis
    if analysis.user_id != current_user.id:
        flash('You can only modify analyses that you own', 'danger')
        return redirect(url_for('dashboard'))
    
    # Toggle public status
    analysis.public = not analysis.public
    db.session.commit()
    
    flash(f'Analysis is now {"public" if analysis.public else "private"}', 'success')
    return redirect(url_for('share_analysis', analysis_id=analysis_id))

@app.route('/delete_analysis/<int:analysis_id>', methods=['POST'])
@login_required
def delete_analysis(analysis_id):
    analysis = Analysis.query.get_or_404(analysis_id)
    
    # Ensure the current user owns this analysis
    if analysis.user_id != current_user.id:
        flash('You can only delete analyses that you own', 'danger')
        return redirect(url_for('dashboard'))
    
    db.session.delete(analysis)
    db.session.commit()
    
    flash('Analysis deleted successfully', 'success')
    return redirect(url_for('dashboard'))

def create_admin_users():
    """Create admin users if they don't exist"""
    with app.app_context():
        # Create admin1
        admin1 = User.query.filter_by(username='admin1').first()
        if not admin1:
            admin1 = User(
                username='admin1',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin1')
            )
            db.session.add(admin1)
            print("Created admin1 user")

        # Create admin2
        admin2 = User.query.filter_by(username='admin2').first()
        if not admin2:
            admin2 = User(
                username='admin2',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin2')
            )
            db.session.add(admin2)
            print("Created admin2 user")

        try:
            db.session.commit()
            print("Admin users initialized successfully")
        except Exception as e:
            db.session.rollback()
            print(f"Error creating admin users: {e}")

if __name__ == '__main__':
    # Create database tables
    with app.app_context():
        db.create_all()
        create_admin_users()

    app.run(debug=True, port=5001)
